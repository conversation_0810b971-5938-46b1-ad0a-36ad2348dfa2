class LoginResponse {
  bool? status;
  String? message;
  String? token;
  UserData? data;

  LoginResponse({this.status, this.message, this.token, this.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      status: json['status'],
      message: json['message'],
      token: json['token'],
      data: json['data'] != null ? UserData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'token': token,
      'data': data?.toJson(),
    };
  }
}

class UserData {
  int? id;
  String? email;
  bool? isProfileCreated;

  UserData({this.id, this.email, this.isProfileCreated});

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'],
      email: json['email'],
      isProfileCreated: json['is_profile_created'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'email': email, 'is_profile_created': isProfileCreated};
  }
}
