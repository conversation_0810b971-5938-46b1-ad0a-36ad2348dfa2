import 'dart:convert';
import 'package:mime/mime.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';

class ChatImagePreview extends StatefulWidget {
  final dynamic args;
  const ChatImagePreview({super.key, this.args});
  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)!.settings.arguments;
    return ChatImagePreview(args: args);
  }

  @override
  State<ChatImagePreview> createState() => _ChatImagePreviewState();
}

class _ChatImagePreviewState extends State<ChatImagePreview> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(children: [
        // Appbar(
        //   title: Lang.of(context).lbl_preview,
        //   hasLeadingIcon: true,
        // ),
        _buildImage(),
        const Spacer(),
        _buildsendbutton(),
        buildSizedBoxH(20.0)
      ]),
    );
  }

  Widget _buildImage() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.0.r),
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: double.infinity, maxHeight: MediaQuery.of(context).size.height / 2),
            child: Image.file(
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
              height: MediaQuery.of(context).size.height / 2,
              width: double.infinity,
              File(
                widget.args[0].path.toString(),
              ),
            ),
          )),
    );
  }

  Widget _buildsendbutton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.0.w),
      child: CustomElevatedButton(
        onPressed: () async {
          if (widget.args[0] != null && widget.args[0].path.isNotEmpty) {
            String? mimeType = lookupMimeType(widget.args[0].path);
            if (mimeType != null) {
              File imageFile = File(widget.args[0].path);
              List<int> imageBytes = await imageFile.readAsBytes();
              String base64Image = base64Encode(imageBytes);
              String dataUri = 'data:$mimeType;base64,$base64Image';
              context.read<ChatBloc>().add(SendMessageEvent(
                    file: dataUri,
                    message: "",
                    touserId: int.tryParse(widget.args[1]) ?? 0,
                    type: 'image',
                  ));
              NavigatorService.goBack();
            } else {
              Logger.lOG('Could not determine MIME type.');
            }
          }
        },
        text: 'Send',
        // text: Lang.of(context).lbl_send,
        width: double.infinity,
      ),
    );
  }
}
