import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/models/common_model/common_model.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';

class LikeRepository {
  final ApiClient apiClient;
  LikeRepository({required this.apiClient});

  Future<MoveOutModel> getMoveOutData() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getMoveOutProfiles,
      );
      return MoveOutModel.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<MoveInModel> getMoveInData() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getMoveInProfiles,
      );
      return MoveInModel.from<PERSON>son(response);
    } catch (error) {
      rethrow;
    }
  }

  Future<CommonModel> acceptLike({
    required int likeProfileId,
    required bool isAccept,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'like_profile_id': likeProfileId,
        'is_accepted': isAccept,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.acceptLikeUrl,
        data: data,
      );
      return CommonModel.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }
}
