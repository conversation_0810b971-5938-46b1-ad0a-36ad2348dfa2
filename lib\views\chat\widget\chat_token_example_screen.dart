import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/chat_screen.dart';
import 'package:room_eight/views/chat/widget/chat_with_token_dialog.dart';
import 'package:room_eight/views/chat/widget/user_search_dialog.dart';

/// Example screen demonstrating how to use the chat with token functionality
class ChatTokenExampleScreen extends StatefulWidget {
  const ChatTokenExampleScreen({super.key});

  @override
  State<ChatTokenExampleScreen> createState() => _ChatTokenExampleScreenState();
}

class _ChatTokenExampleScreenState extends State<ChatTokenExampleScreen> {
  final TextEditingController _tokenController = TextEditingController();

  @override
  void dispose() {
    _tokenController.dispose();
    super.dispose();
  }

  void _startChatDirectly() {
    final token = _tokenController.text.trim();
    if (token.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a user token or username'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat with User Token'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Chat with Another User',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'You can start a chat with another user by entering their username or token. '
              'The app will search for the user and open a chat conversation if found.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),

            // Method 1: Using the search dialog
            const Text(
              'Method 1: Search Users Dialog',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text(
              'Click the button below to open a dialog where you can search for users:',
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.showUserSearchDialog(),
              icon: const Icon(Icons.search),
              label: const Text('Search Users'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            ),

            const SizedBox(height: 16),
            const Text('Or use the token dialog:'),
            const SizedBox(height: 8),
            const ChatWithTokenButton(
              label: 'Open Token Dialog',
              icon: Icons.chat_bubble_outline,
            ),

            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 32),

            // Method 2: Direct input
            const Text(
              'Method 2: Direct Input',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text(
              'Enter a username or token directly below and click "Start Chat":',
            ),
            const SizedBox(height: 16),
            CustomTextInputField(
              controller: _tokenController,
              hintLabel: 'Enter username or token (e.g., john_doe)',
              context: context,
              type: InputType.text,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _startChatDirectly,
              icon: const Icon(Icons.send),
              label: const Text('Start Chat'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),

            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 16),

            // Instructions
            const Text(
              'How it works:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Enter a username or token of the user you want to chat with\n'
              '2. The app searches for the user using the socket search functionality\n'
              '3. If the user is found, a chat screen opens automatically\n'
              '4. If the user is not found, an error message is displayed\n'
              '5. You can then send messages to the user in real-time',
              style: TextStyle(fontSize: 14),
            ),

            const Spacer(),

            // Example tokens (for demonstration)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Example usernames to try:',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• john_doe\n'
                    '• jane_smith\n'
                    '• user123\n'
                    '• test_user',
                    style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Note: These are example usernames. Use actual usernames from your app.',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
