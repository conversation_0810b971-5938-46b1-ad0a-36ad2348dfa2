import 'package:room_eight/core/utils/app_exports.dart';

class StatusChipWidget extends StatelessWidget {
  final String statusLabel;
  final bool isVerified;
  final double? horizontalPadding;
  final double? verticalPadding;
  final double? borderRadius;
  final Color? verifiedColor;
  final Color? unverifiedColor;
  final Color? verifiedBgColor;
  final Color? unverifiedBgColor;
  final TextStyle? textStyle;

  const StatusChipWidget({
    super.key,
    required this.statusLabel,
    required this.isVerified,
    this.horizontalPadding = 8.0,
    this.verticalPadding = 4.0,
    this.borderRadius,
    this.verifiedColor,
    this.unverifiedColor,
    this.verifiedBgColor,
    this.unverifiedBgColor,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final Color containerColor = isVerified
        ? (verifiedBgColor ?? 
           Theme.of(context).customColors.greencolor!.withValues(alpha: 0.1))
        : (unverifiedBgColor ?? 
           Theme.of(context).customColors.redcolor!.withValues(alpha: 0.1));

    final Color textColor = isVerified
        ? (verifiedColor ?? Theme.of(context).customColors.greencolor!)
        : (unverifiedColor ?? Theme.of(context).customColors.redcolor!);

    return Container(
      decoration: BoxDecoration(
        color: containerColor,
        borderRadius: BorderRadius.circular(borderRadius ?? 100.r),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding!,
        vertical: verticalPadding!,
      ),
      child: Center(
        child: Text(
          statusLabel,
          style: textStyle?.copyWith(color: textColor) ??
              Theme.of(context).textTheme.labelSmall!.copyWith(
                color: textColor,
              ),
        ),
      ),
    );
  }
}