import 'package:room_eight/core/utils/app_exports.dart';

/// <PERSON>ript to manually clear Flutter cache manager cache files
/// Run with: dart run scripts/clear_cache.dart
void main() async {
  try {
    Logger.lOG('Starting cache cleanup...');

    // Get the application documents directory
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String cachePath = '${appDocDir.path}/cache';

    Logger.lOG('Cache directory: $cachePath');

    // Check if cache directory exists
    final Directory cacheDir = Directory(cachePath);
    if (await cacheDir.exists()) {
      Logger.lOG('Cache directory found. Clearing...');

      // Delete all cache files
      await cacheDir.delete(recursive: true);
      Logger.lOG('Cache directory deleted successfully.');

      // Recreate the cache directory
      await cacheDir.create(recursive: true);
      Logger.lOG('Cache directory recreated.');
    } else {
      Logger.lOG('Cache directory does not exist. Nothing to clear.');
    }

    // Also try to clear specific cache manager files
    final List<String> cacheFiles = [
      '${appDocDir.path}/RoomEight.db',
      '${appDocDir.path}/RoomEight_V2.db',
      '${appDocDir.path}/RoomEight_V3.db',
    ];

    for (String filePath in cacheFiles) {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        Logger.lOG('Deleted cache file: $filePath');
      }
    }

    Logger.lOG('Cache cleanup completed successfully!');
  } catch (e) {
    Logger.lOG('Error during cache cleanup: $e');
    exit(1);
  }
}
