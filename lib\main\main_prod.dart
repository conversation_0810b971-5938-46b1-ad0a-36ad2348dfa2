import 'package:room_eight/core/app/room_eight_app.dart';
import 'package:room_eight/core/utils/app_exports.dart';

Future<void> main() async {
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    Logger.lOG("Error loading .env file: $e");
  }
  WidgetsFlutterBinding.ensureInitialized();

  // Custom error widget
  ErrorWidget.builder = (FlutterErrorDetails details) {
    return CustomErrorWidget(errorMessage: details.exception.toString());
  };
  // Get device info
  final deviceInfo = DeviceInfoPlugin();

  String? deviceId = '';
  int? deviceOs = 0;

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfo.androidInfo;
    deviceId = androidInfo.id;
    deviceOs = 1;
  } else if (Platform.isIOS) {
    final iosInfo = await deviceInfo.iosInfo;
    deviceId = iosInfo.identifierForVendor;
    deviceOs = 2;
  }

  EnvConfig prodConfig = EnvConfig(
    baseUrl: PROD_SERVER_BASEURL,
    deviceId: deviceId ?? '',
    deviceOs: deviceOs,
    localIdentifier: '',
  );
  // Env config
  FlavorConfig.initialize(flavor: Flavor.prod, env: prodConfig);

  Logger.lOG(
    "FLAVOR             : [36m[1m[4m${FlavorConfig.instance.flavor}\u001b[0m\n"
    "DEVICE ID          : $deviceId\n"
    "DEVICE OS          : $deviceOs\n"
    "BASE URL           : $PROD_SERVER_BASEURL",
  );

  try {
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocDir.path);

    // Clear corrupted cache on startup if needed
    try {
      await CacheUtils.clearCacheByKey('RoomEight');
      Logger.lOG('Cleared potentially corrupted cache on startup');
    } catch (e) {
      Logger.lOG('Cache clearing on startup failed (this is normal): $e');
    }

    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    await Hive.openBox(
      'RoomEightBox',
    ).then((value) => runApp(RoomEightApp(prefs: value)));
  } catch (e, stackTrace) {
    Logger.lOG('Error initializing app: $e\n$stackTrace');
  }
}
