import 'package:equatable/equatable.dart';
import 'package:room_eight/models/splash_model/initialize_model.dart';
import 'package:room_eight/core/utils/app_exports.dart';

part 'splash_event.dart';
part 'splash_state.dart';

class SplashBloc extends Bloc<SplashEvent, SplashState> {
  final AuthRepository authRepository;

  SplashBloc(this.authRepository) : super(const SplashState()) {
    on<SalesAppAuthIntilizeEvent>(_onSalesAppAuthIntilize);
  }

  Future<void> _onSalesAppAuthIntilize(
    SalesAppAuthIntilizeEvent event,
    Emitter<SplashState> emit,
  ) async {
    try {
      // SalesAppAuthIntilizeModel salesAppAuthIntilizeModel = await authRepository
      //     .initializeApiCall(
      //       deviceId: FlavorConfig.instance.env.deviceId,
      //       deviceOS: FlavorConfig.instance.env.deviceOs,
      //     );
      Prefobj.preferences?.put(
        Prefkeys.CALLER_IDENTIFIER,
        ''
        // salesAppAuthIntilizeModel.callerIdentifier,
      );
      // emit(
      //   state.copyWith(salesAppAuthIntilizeModel: salesAppAuthIntilizeModel),
      // );
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }
}
