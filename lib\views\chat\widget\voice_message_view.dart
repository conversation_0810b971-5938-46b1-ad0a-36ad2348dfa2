// import 'dart:async';

// import 'package:flowkar/core/utils/exports.dart';

// class VoiceMessageView extends StatefulWidget {
//   const VoiceMessageView({
//     super.key,
//     required this.screenWidth,
//     this.message,
//     required this.isMessageBySender,
//     this.onMaxDuration,
//   });

//   final double screenWidth;
//   final String? message;
//   final Function(int)? onMaxDuration;
//   final bool isMessageBySender;

//   @override
//   State<VoiceMessageView> createState() => _VoiceMessageViewState();
// }

// class _VoiceMessageViewState extends State<VoiceMessageView> {
//   late PlayerController controller;
//   late StreamSubscription<PlayerState> playerStateSubscription;

//   final ValueNotifier<PlayerState> _playerState =
//       ValueNotifier(PlayerState.stopped);
//   static _VoiceMessageViewState? activePlayer;

//   PlayerState get playerState => _playerState.value;

//   PlayerWaveStyle playerWaveStyle = PlayerWaveStyle(
//       backgroundColor: Colors.black,
      
//       scaleFactor: 70,
//       fixedWaveColor: AppColors.blackcolor.withOpacity(0.2),
//       liveWaveColor: AppColors.blackcolor);

//   bool _isLoading = false;
//   PlayerController? _currentController;

//   @override
//   void initState() {
//     _loadAudio();
//     super.initState();
   
//   }

//   void _disposeController() {
//     if (_currentController != null) {
//       _currentController!.dispose();
//       _currentController = null;
//     }
//   }

//   Future<void> _loadAudio() async {
//     if (widget.message != null && widget.message!.isNotEmpty) {
//       final String url = widget.message!;

//       setState(() {
//         _isLoading = true;
//       });

//       try {
//         final localFilePath = await downloadFile(url);

//         _disposeController();

//         controller = PlayerController()
//           ..preparePlayer(
//             path: localFilePath,
//             noOfSamples:
//                 playerWaveStyle.getSamplesForWidth(widget.screenWidth * 0.5),
//           ).whenComplete(() {
//             widget.onMaxDuration?.call(controller.maxDuration);
//             setState(() {
//               _isLoading = false;
//             });
//           });

//         _currentController = controller;

//         playerStateSubscription = controller.onPlayerStateChanged
//             .listen((state) => _playerState.value = state);
//       } catch (error) {
//         debugPrint('Error while preparing player: $error');
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     } else {
//       Logger.lOG("Invalid URL");
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       clipBehavior: Clip.none,
//       children: [
//         Container(
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(12),
//               color: Colors.transparent),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               ValueListenableBuilder<PlayerState>(
//                 valueListenable: _playerState,
//                 builder: (context, state, child) {
//                   return IconButton(
//                     onPressed: _playOrPause,
//                     icon:
//                         state.isStopped || state.isPaused || state.isInitialised
//                             ? const Icon(
//                                 Icons.play_arrow,
//                                 color: AppColors.blackcolor,
//                               )
//                             : const Icon(
//                                 Icons.stop,
//                                 color: AppColors.blackcolor,
//                               ),
//                   );
//                 },
//               ),
//               if (_isLoading) buildSizedBoxH(60),
//               if (_isLoading)
//                 buildSizedboxW(widget.screenWidth * 0.53)
//               else if (widget.message != null && widget.message!.isNotEmpty)
//                 AudioFileWaveforms(
//                   size: Size(widget.screenWidth * 0.50, 60),
//                   playerController: controller,
//                   waveformType: WaveformType.fitWidth,
//                   playerWaveStyle: playerWaveStyle,
//                   padding: const EdgeInsets.only(right: 10),
//                   animationCurve: Curves.easeIn,
//                   animationDuration: const Duration(milliseconds: 500),
//                   enableSeekGesture: true,
//                 )
//               else
//                 const SizedBox.shrink(),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Future<void> _playOrPause() async {
//     assert(
//       defaultTargetPlatform == TargetPlatform.iOS ||
//           defaultTargetPlatform == TargetPlatform.android,
//       "Voice messages are only supported on Android and iOS platforms",
//     );
//     if (widget.message == null || widget.message!.isEmpty) return;

//     // Pause any currently active player before starting this one
//     if (activePlayer != null && activePlayer != this) {
//       activePlayer!.controller
//           .pausePlayer(); // Pause the currently playing player
//     }

//     // Set this as the active player
//     activePlayer = this;

//     if (playerState.isInitialised ||
//         playerState.isPaused ||
//         playerState.isStopped) {
//       controller.startPlayer(finishMode: FinishMode.pause);
//     } else {
//       controller.pausePlayer();
//     }
//   }

//   Future<String> downloadFile(String url) async {
//     final dio = Dio();
//     final directory = await getTemporaryDirectory();
//     final filePath =
//         '${directory.path}/${const Uuid().v4()}.mp4';

//     try {
//       final response = await dio.download(url, filePath);
//       if (response.statusCode == 200) {
//         return filePath;
//       } else {
//         throw Exception('Failed to download file');
//       }
//     } catch (e) {
//       Logger.lOG('Error downloading file: $e');
//       rethrow;
//     }
//   }
// }
