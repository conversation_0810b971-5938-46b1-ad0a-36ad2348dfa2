import 'package:room_eight/core/utils/app_exports.dart';

class UserDetailShimmer extends StatelessWidget {
  const UserDetailShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildShimmerUserBasicInfo(context, customColors),
          buildSizedBoxH(16.h),
          _buildShimmerOptionsList(context, customColors),
          buildSizedBoxH(16.h),
          _buildShimmerActionButtons(context, customColors),
          buildSizedBoxH(16.h),
        ],
      ),
    );
  }

  Widget _buildShimmerUserBasicInfo(
    BuildContext context,
    dynamic customColors,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          // Image carousel shimmer
          Shimmer.fromColors(
            baseColor:
                customColors.lightGreyTextColor?.withOpacity(0.3) ??
                Colors.grey[300]!,
            highlightColor:
                customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
            child: Container(
              height: 210.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
            ),
          ),
          buildSizedBoxH(8.h),
          // Indicator dots shimmer
          Shimmer.fromColors(
            baseColor:
                customColors.lightGreyTextColor?.withOpacity(0.3) ??
                Colors.grey[300]!,
            highlightColor:
                customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                3,
                (index) => Container(
                  margin: EdgeInsets.symmetric(horizontal: 1.5.w),
                  width: index == 0 ? 18.w : 6.w,
                  height: 6.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(index == 0 ? 8 : 4),
                  ),
                ),
              ),
            ),
          ),
          buildSizedBoxH(16.h),
          // Name shimmer
          Shimmer.fromColors(
            baseColor:
                customColors.lightGreyTextColor?.withOpacity(0.3) ??
                Colors.grey[300]!,
            highlightColor:
                customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
            child: Container(
              height: 24.h,
              width: 150.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          buildSizedBoxH(4.h),
          // Role shimmer
          Shimmer.fromColors(
            baseColor:
                customColors.lightGreyTextColor?.withOpacity(0.3) ??
                Colors.grey[300]!,
            highlightColor:
                customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
            child: Container(
              height: 18.h,
              width: 100.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          buildSizedBoxH(16.h),
          // About shimmer (multiple lines)
          Shimmer.fromColors(
            baseColor:
                customColors.lightGreyTextColor?.withOpacity(0.3) ??
                Colors.grey[300]!,
            highlightColor:
                customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
            child: Column(
              children: [
                Container(
                  height: 16.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                buildSizedBoxH(4.h),
                Container(
                  height: 16.h,
                  width: 250.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                buildSizedBoxH(4.h),
                Container(
                  height: 16.h,
                  width: 180.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerOptionsList(BuildContext context, dynamic customColors) {
    return Wrap(
      children: List.generate(
        6, // Show 6 shimmer chips
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildShimmerOptionChip(context, customColors, index),
        ),
      ),
    );
  }

  Widget _buildShimmerOptionChip(
    BuildContext context,
    dynamic customColors,
    int chipIndex,
  ) {
    return Shimmer.fromColors(
      baseColor:
          customColors.lightGreyTextColor?.withOpacity(0.3) ??
          Colors.grey[300]!,
      highlightColor:
          customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 20.h,
              width: 20.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
            buildSizedboxW(5.w),
            Container(
              height: 16.h,
              width:
                  60.w +
                  (chipIndex * 10).w, // Varying widths for different chips
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerActionButtons(
    BuildContext context,
    dynamic customColors,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Shimmer.fromColors(
              baseColor:
                  customColors.lightGreyTextColor?.withOpacity(0.3) ??
                  Colors.grey[300]!,
              highlightColor:
                  customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
              child: Container(
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
          buildSizedboxW(12.w),
          Expanded(
            child: Shimmer.fromColors(
              baseColor:
                  customColors.lightGreyTextColor?.withOpacity(0.3) ??
                  Colors.grey[300]!,
              highlightColor:
                  customColors.fillColor?.withOpacity(0.5) ?? Colors.grey[100]!,
              child: Container(
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods (assuming these exist in your codebase)
  Widget buildSizedBoxH(double height) {
    return SizedBox(height: height);
  }

  Widget buildSizedboxW(double width) {
    return SizedBox(width: width);
  }
}

// Usage example in your bottom sheet:
class UserDetailBottomSheet extends StatelessWidget {
  final bool isLoading;
  final Widget contentWidget; // Pass your actual content widget

  const UserDetailBottomSheet({
    Key? key,
    this.isLoading = false,
    required this.contentWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: isLoading
          ? const UserDetailShimmer()
          : contentWidget, // Your existing content widget
    );
  }
}
