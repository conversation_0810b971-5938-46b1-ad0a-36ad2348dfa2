import 'package:room_eight/core/utils/app_exports.dart';

class GenderPickerDialog extends StatefulWidget {
  final String initialGender;
  const GenderPickerDialog({super.key, required this.initialGender});

  @override
  State<GenderPickerDialog> createState() => _GenderPickerDialogState();
}

class _GenderPickerDialogState extends State<GenderPickerDialog>
    with TickerProviderStateMixin {
  late String selectedGender;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final List<GenderOption> genderOptions = [
    GenderOption(
      id: 'Male',
      label: 'Male',
      icon: Icons.male,
      description: 'Male identity',
    ),
    GenderOption(
      id: 'Female',
      label: 'Female',
      icon: Icons.female,
      description: 'Female identity',
    ),
    GenderOption(
      id: 'Other',
      label: 'Other',
      icon: Icons.transgender,
      description: 'Other gender identity',
    ),
  ];

  @override
  void initState() {
    super.initState();
    selectedGender = widget.initialGender.isNotEmpty
        ? widget.initialGender
        : 'Male';

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onGenderChanged(String newGender) {
    if (mounted) {
      setState(() {
        selectedGender = newGender;
      });
      // Add haptic feedback for better UX
      HapticFeedback.selectionClick();
    }
  }

  void _onCancel() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context);
    }
  }

  void _onSelect() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context, selectedGender);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 24,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: theme.customColors.fillColor,
              borderRadius: BorderRadius.circular(28),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.15),
                  blurRadius: 24,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.15 : 0.08),
                  blurRadius: 6,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Text(
                    'Select Your Gender',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),

                  buildSizedBoxH(16),

                  // Gender options
                  Column(
                    children: genderOptions.asMap().entries.map((entry) {
                      final index = entry.key;
                      final option = entry.value;
                      final isSelected = option.id == selectedGender;

                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: index < genderOptions.length - 1 ? 12.0 : 0.0,
                        ),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeOutCubic,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  )
                                : theme.customColors.fillColor!.withValues(
                                    alpha: 0.3,
                                  ),
                            borderRadius: BorderRadius.circular(16),
                            border: isSelected
                                ? Border.all(
                                    color: theme.colorScheme.primary.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1.5,
                                  )
                                : null,
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () => _onGenderChanged(option.id),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 16,
                                ),
                                child: Row(
                                  children: [
                                    // Icon container
                                    AnimatedContainer(
                                      duration: const Duration(
                                        milliseconds: 200,
                                      ),
                                      width: 48.w,
                                      height: 48.h,
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? theme.colorScheme.primary
                                            : theme
                                                  .customColors
                                                  .greycontainercolor,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        option.icon,
                                        size: 24,
                                        color: isSelected
                                            ? theme.colorScheme.onPrimary
                                            : theme
                                                  .colorScheme
                                                  .onSurfaceVariant,
                                      ),
                                    ),
                                    buildSizedboxW(16),

                                    // Text content
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          AnimatedDefaultTextStyle(
                                            duration: const Duration(
                                              milliseconds: 200,
                                            ),
                                            style: theme.textTheme.titleMedium!
                                                .copyWith(
                                                  color: isSelected
                                                      ? theme
                                                            .colorScheme
                                                            .primary
                                                      : theme
                                                            .colorScheme
                                                            .onSurface,
                                                  fontWeight: isSelected
                                                      ? FontWeight.w600
                                                      : FontWeight.w500,
                                                ),
                                            child: Text(option.label),
                                          ),
                                          buildSizedBoxH(2),
                                          Text(
                                            option.description,
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: theme
                                                      .colorScheme
                                                      .onSurface
                                                      .withValues(alpha: 0.6),
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Selection indicator
                                    AnimatedScale(
                                      duration: const Duration(
                                        milliseconds: 200,
                                      ),
                                      scale: isSelected ? 1.0 : 0.0,
                                      child: Container(
                                        width: 20.w,
                                        height: 20.h,
                                        decoration: BoxDecoration(
                                          color: theme.colorScheme.primary,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.check,
                                          size: 14,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  buildSizedBoxH(24),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: CustomElevatedButton(
                          text: 'Cancel',
                          onPressed: _onCancel,
                          secondary: true,
                          buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
                            context,
                          ),
                        ),
                      ),
                      buildSizedboxW(12),
                      Expanded(
                        child: CustomElevatedButton(
                          onPressed: _onSelect,
                          text: 'Confirm',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class GenderOption {
  final String id;
  final String label;
  final IconData icon;
  final String description;

  const GenderOption({
    required this.id,
    required this.label,
    required this.icon,
    required this.description,
  });
}
