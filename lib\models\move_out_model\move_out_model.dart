class MoveOutModel {
  bool? status;
  String? message;
  List<MoveOutUser>? data;

  MoveOutModel({this.status, this.message, this.data});

  factory MoveOutModel.fromJson(Map<String, dynamic> json) {
    return MoveOutModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? List<MoveOutUser>.from(
              json['data'].map((item) => MoveOutUser.fromJson(item)),
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }
}

class MoveOutUser {
  int? id;
  String? name;
  String? year;
  bool? status;
  String? profileImage;
  String? about;

  MoveOutUser({
    this.id,
    this.name,
    this.year,
    this.status,
    this.profileImage,
    this.about,
  });

  factory MoveOutUser.fromJson(Map<String, dynamic> json) {
    return MoveOutUser(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      year: json['year']?.toString() ?? '',
      status: json['status'] ?? false,
      profileImage: json['profile_image'] ?? '', // Handle missing field
      about: json['about'] ?? '', // Handle missing field
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'year': year,
      'status': status,
      'profile_image': profileImage,
      'about': about,
    };
  }

  // Helper method to get display name
  String get displayName => name?.isNotEmpty == true ? name! : 'Unknown User';

  // Helper method to get profile picture URL
  String? get profileImageUrl =>
      profileImage?.isNotEmpty == true ? profileImage : null;

  // Helper method to check if user is active
  bool get isActive => status == true;

  // Helper method for department (using about field)
  String? get department => about;
}
