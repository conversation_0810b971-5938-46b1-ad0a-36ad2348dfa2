import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:room_eight/core/utils/cache_utils.dart';
import 'package:room_eight/widgets/common_widget/image_view.dart';

void main() {
  group('Cache Manager Tests', () {
    test('Cache manager should be created without errors', () {
      expect(() => CustomImageView.getCacheManager(), returnsNormally);
    });

    test('Cache utils should clear cache without errors', () async {
      await expectLater(
        CacheUtils.clearCacheByKey('test_cache'),
        completes,
      );
    });

    test('Cache manager should handle corrupted cache gracefully', () {
      // Test that multiple calls to getCacheManager return the same instance
      final manager1 = CustomImageView.getCacheManager();
      final manager2 = CustomImageView.getCacheManager();
      
      expect(manager1, equals(manager2));
      expect(manager1, isA<CacheManager>());
    });

    test('Cache utils should clear all caches without errors', () async {
      await expectLater(
        CacheUtils.clearAllCaches(),
        completes,
      );
    });
  });
}
