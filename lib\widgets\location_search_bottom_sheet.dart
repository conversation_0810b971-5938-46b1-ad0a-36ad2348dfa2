import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/loading_animation_widget.dart';
import 'package:room_eight/models/location_model/location_model.dart';
import 'package:room_eight/services/geocoding_service.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';

class LocationSearchBottomSheet extends StatefulWidget {
  const LocationSearchBottomSheet({super.key});

  @override
  State<LocationSearchBottomSheet> createState() =>
      _LocationSearchBottomSheetState();
}

class _LocationSearchBottomSheetState extends State<LocationSearchBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<LocationModel> _searchResults = [];
  bool _isSearching = false;
  bool _hasSearched = false;

  @override
  void initState() {
    super.initState();
    _loadPopularLocations();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadPopularLocations() async {
    setState(() {
      _isSearching = true;
    });

    try {
      final popularLocations = await GeocodingService.getPopularLocations();
      if (mounted) {
        setState(() {
          _searchResults = popularLocations;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      _loadPopularLocations();
      return;
    }

    setState(() {
      _isSearching = true;
      _hasSearched = true;
    });

    try {
      // Use the enhanced search with worldwide fallback
      final results = await GeocodingService.searchWithFallback(query);
      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  void _addLocation(LocationModel location) {
    context.read<ProfileBloc>().add(AddLocation(location));
    Navigator.of(context).pop();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isSearching = true;
    });

    try {
      final currentLocation = await GeocodingService.getCurrentLocation();
      if (currentLocation != null && mounted) {
        _addLocation(currentLocation);
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unable to get current location. Please check location permissions.',
            ),
            backgroundColor: Theme.of(context).customColors.redcolor,
          ),
        );
        setState(() {
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: ${e.toString()}'),
            backgroundColor: Theme.of(context).customColors.redcolor,
          ),
        );
        setState(() {
          _isSearching = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheetWidget(
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Title and close button
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Text(
                    'Search Location',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).customColors.blackColor,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).customColors.blackColor,
                    ),
                  ),
                ],
              ),
            ),

            // Search field
            Padding(
              padding: EdgeInsets.all(16.w),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: 'Search for a location...',
                  hintStyle: TextStyle(
                    color: Theme.of(context).customColors.lightgreycolor,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context).customColors.primaryColor,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _performSearch('');
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Theme.of(
                              context,
                            ).customColors.lightgreycolor,
                          ),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: Theme.of(context).customColors.lightgreycolor!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                    borderSide: BorderSide(
                      color: Theme.of(context).customColors.primaryColor!,
                      width: 2,
                    ),
                  ),
                ),
                onChanged: _performSearch,
              ),
            ),

            // Current Location Button
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: GestureDetector(
                onTap: _getCurrentLocation,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).customColors.primaryColor!.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Theme.of(context).customColors.primaryColor!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.my_location,
                        color: Theme.of(context).customColors.primaryColor,
                        size: 20,
                      ),
                      buildSizedboxW(8.w),
                      Text(
                        'Use Current Location',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).customColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            buildSizedBoxH(16.h),

            // Search results
            Expanded(
              child: _isSearching
                  ? Center(
                      child: LoadingAnimationWidget(
                        // color: Theme.of(context).customColors.primaryColor,
                      ),
                    )
                  : _searchResults.isEmpty && _hasSearched
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.location_off,
                            size: 48,
                            color: Theme.of(
                              context,
                            ).customColors.lightgreycolor,
                          ),
                          buildSizedBoxH(16.h),
                          Text(
                            'No locations found',
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.lightgreycolor,
                                ),
                          ),
                          buildSizedBoxH(8.h),
                          Text(
                            'Try searching with different keywords',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.lightgreycolor,
                                ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final location = _searchResults[index];
                        return Container(
                          margin: EdgeInsets.only(bottom: 8.h),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Theme.of(context)
                                  .customColors
                                  .lightgreycolor!
                                  .withValues(alpha: 0.3),
                            ),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: ListTile(
                            leading: Icon(
                              Icons.location_on,
                              color: Theme.of(
                                context,
                              ).customColors.primaryColor,
                            ),
                            title: Text(
                              location.name,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            subtitle: location.address != null
                                ? Text(
                                    location.address!,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(
                                          color: Theme.of(
                                            context,
                                          ).customColors.lightgreycolor,
                                        ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                : null,
                            onTap: () => _addLocation(location),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
