import 'package:room_eight/core/utils/app_exports.dart';

class AgePickerDialog extends StatefulWidget {
  final int initialAge;
  const AgePickerDialog({super.key, required this.initialAge});

  @override
  State<AgePickerDialog> createState() => _AgePickerDialogState();
}

class _AgePickerDialogState extends State<AgePickerDialog>
    with TickerProviderStateMixin {
  late int selectedAge;
  late FixedExtentScrollController _controller;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    selectedAge = widget.initialAge;
    _controller = FixedExtentScrollController(
      initialItem: (selectedAge - 16).clamp(0, 84),
    );

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onAgeChanged(int newAge) {
    if (mounted) {
      setState(() {
        selectedAge = newAge;
      });
      // Add haptic feedback for better UX
      HapticFeedback.selectionClick();
    }
  }

  void _onCancel() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context);
    }
  }

  void _onSelect() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context, selectedAge);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 24,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(28),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.15),
                  blurRadius: 24,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.15 : 0.08),
                  blurRadius: 6,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Text(
                    'Select Your Age',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),

                  buildSizedBoxH(24),

                  // Age selector with enhanced design
                  Container(
                    height: 200.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Theme.of(
                        context,
                      ).customColors.fillColor?.withValues(alpha: 0.3),
                    ),
                    child: Stack(
                      children: [
                        // Selection highlight
                        // Positioned.fill(
                        //   child: Center(
                        //     child: Container(
                        //       height: 50,
                        //       margin: const EdgeInsets.symmetric(
                        //         horizontal: 16,
                        //       ),
                        //       decoration: BoxDecoration(
                        //         color: theme.colorScheme.primary.withValues(
                        // alpha:
                        //           0.1,
                        //         ),
                        //         borderRadius: BorderRadius.circular(12),
                        //         border: Border.all(
                        //           color: theme.colorScheme.primary.withValues(
                        // alpha:
                        //             0.3,
                        //           ),
                        //           width: 1.5,
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                        // ),

                        // List wheel
                        ListWheelScrollView.useDelegate(
                          controller: _controller,
                          itemExtent: 50,
                          physics: const FixedExtentScrollPhysics(),
                          diameterRatio: 1.5,
                          magnification: 1.0,
                          onSelectedItemChanged: (index) {
                            _onAgeChanged(16 + index);
                          },
                          childDelegate: ListWheelChildBuilderDelegate(
                            builder: (context, index) {
                              final age = 16 + index;
                              if (age > 100) return null;

                              final isSelected = age == selectedAge;
                              final distance = (age - selectedAge).abs();
                              final opacity = distance == 0
                                  ? 1.0
                                  : distance == 1
                                  ? 0.6
                                  : 0.3;

                              return AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                curve: Curves.easeOutCubic,
                                child: Center(
                                  child: AnimatedDefaultTextStyle(
                                    duration: const Duration(milliseconds: 200),
                                    curve: Curves.easeOutCubic,
                                    style: theme.textTheme.headlineMedium!
                                        .copyWith(
                                          color: isSelected
                                              ? theme.colorScheme.primary
                                              : theme.colorScheme.onSurface
                                                    .withValues(alpha: opacity),
                                          fontWeight: isSelected
                                              ? FontWeight.w700
                                              : FontWeight.w500,
                                          fontSize: isSelected ? 24 : 18,
                                          height: 1.2,
                                        ),
                                    child: Text(
                                      age.toString(),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        // Gradient overlays for fade effect
                        Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          height: 40.h,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Theme.of(context).customColors.fillColor
                                          ?.withValues(alpha: 0.3) ??
                                      Colors.transparent,
                                  Theme.of(context).customColors.fillColor
                                          ?.withValues(alpha: 0.0) ??
                                      Colors.transparent,
                                ],
                              ),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: 40.h,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                colors: [
                                  Theme.of(context).customColors.fillColor
                                          ?.withValues(alpha: 0.3) ??
                                      Colors.transparent,
                                  Theme.of(context).customColors.fillColor
                                          ?.withValues(alpha: 0.0) ??
                                      Colors.transparent,
                                ],
                              ),
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(16),
                                bottomRight: Radius.circular(16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  buildSizedBoxH(24),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: CustomElevatedButton(
                          onPressed: _onCancel,
                          text: 'Cancel',
                          secondary: true,
                          buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
                            context,
                          ),
                        ),
                      ),
                      buildSizedboxW(12),
                      Expanded(
                        child: CustomElevatedButton(
                          onPressed: _onSelect,
                          text: 'Confirm',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
