import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/login_view/login_screen.dart';

class SplashScreen extends StatelessWidget {
  static Widget builder(BuildContext context) {
    return const SplashScreen();
  }

  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SplashBloc, SplashState>(
      builder: (context, state) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            systemNavigationBarIconBrightness: Brightness.light,
            // systemNavigationBarColor: Colors.transparent,
            systemNavigationBarColor: Theme.of(
              context,
            ).customColors.primaryColor,
          ),
          child: BackgroundImage(
            imagePath: Assets.images.pngs.other.pngSplashBg.path,
            child: AnimatedSplashScreen(
              duration: 3000,
              splashIconSize: 100.0,
              splash: CustomImageView(
                fit: BoxFit.contain,
                width: MediaQuery.of(context).size.width * 0.4,
                imagePath: Assets.images.pngs.other.pngAppLogo.path,
              ),
              backgroundColor: Colors.transparent,
              nextScreen: () {
                final isLogin =
                    (Prefobj.preferences?.get(Prefkeys.IS_LOGIN)) ?? false;

                final onboardingDone =
                    Prefobj.preferences?.get(Prefkeys.ONBOARDING) == true;

                if (!isLogin) {
                  return onboardingDone
                      ? LoginScreen.builder(context)
                      : OnboardingScreen.builder(context);
                } else {
                  return RoomEightNavBar.builder(context);
                }
              }(),
              splashTransition: SplashTransition.scaleTransition,
            ),
          ),
        );
      },
    );
  }
}
