import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/custom_widget/status_chip_widget.dart';

class HorizontalTileWidget extends StatelessWidget {
  final String imagePath;
  final Color? iconColor;
  final String title;
  final String? subtitle; 
  final String? heading;
  final bool? isVerified;
  final String? statusLabel;
  final VoidCallback? onTap;
  final bool showArrow;

  const HorizontalTileWidget({
    super.key,
    required this.imagePath,
    this.iconColor,
    required this.title,
    this.subtitle,
    this.heading,
    this.isVerified,
    this.statusLabel,
    this.onTap,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (heading != null)
          Column(
            children: [
              buildSizedBoxH(24.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                child: Text(
                  heading!,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 20.sp,
                  ),
                ),
              ),
            ],
          ),
        InkWell(
          splashColor: Theme.of(context).customColors.greycontainercolor,
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        height: 50.h,
                        width: 50.w,
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).customColors.greycontainercolor,
                          shape: BoxShape.circle,
                        ),
                        child: CustomImageView(
                          margin: EdgeInsets.all(10.0),
                          imagePath: imagePath,
                          color:
                              iconColor ??
                              Theme.of(context).customColors.primaryColor,
                        ),
                      ),
                      buildSizedboxW(8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              title,
                              maxLines: subtitle != null
                                  ? 1
                                  : 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyMedium!
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                            if (subtitle != null && subtitle!.isNotEmpty) ...[
                              buildSizedBoxH(
                                2.h,
                              ),
                              Text(
                                subtitle!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: Theme.of(
                                        context,
                                      ).customColors.hinttextcolor,
                                    ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Right side content
                Row(
                  children: [
                    if (statusLabel != null && statusLabel!.isNotEmpty)
                      StatusChipWidget(
                        statusLabel: statusLabel!,
                        isVerified: isVerified ?? false,
                      ),
                    if (statusLabel != null &&
                        statusLabel!.isNotEmpty &&
                        showArrow)
                      buildSizedboxW(8.w),

                    if (showArrow)
                      CustomImageView(
                        imagePath: Assets.images.svgs.icons.icArrowRight.path,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}