import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class AddPersonalDetailScreen3Shimmer extends StatelessWidget {
  const AddPersonalDetailScreen3Shimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Theme.of(context).customColors.fillColor,
      body: BackgroundImage(
        imagePath: Assets.images.pngs.other.pngAuthBg4.path,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTopSection(context),
                buildSizedBoxH(25.h),
                Expanded(child: _buildSelectionMenuLists(context)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Keep the actual back button without shimmer
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
          bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
          fillColor: customColors.fillColor!.withAlpha(75),
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icBackArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionMenuLists(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.fillColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDragHandle(context),
                buildSizedBoxH(24.h),
                Expanded(child: _buildShimmerContent(context)),
                buildSizedBoxH(24.h),
                _buildShimmerButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 26.h,
              width: 250.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          buildSizedBoxH(10.h),
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 15.h,
              width: 375.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          buildSizedBoxH(10.h),
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 15.h,
              width: 100.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSizedBoxH(10.h),
          _buildShimmerCheckbox(context),
          buildSizedBoxH(15.h),
          _buildShimmerTextField(context),
          buildSizedBoxH(15.h),
          _buildShimmerSection(context),
          buildSizedBoxH(15.h),
          _buildShimmerSection(context),
          // Shimmer.fromColors(),
          buildSizedBoxH(15.h),
          _buildShimmerSection(context),
          buildSizedBoxH(15.h),
          _buildShimmerSection(context),
        ],
      ),
    );
  }

  Widget _buildShimmerCheckbox(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 20.h,
            width: 20.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(3.r),
            ),
          ),
        ),
        buildSizedboxW(8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 16.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
              buildSizedBoxH(4.h),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 16.h,
                  width: 180.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerTextField(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 55.h,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(100.r),
        ),
      ),
    );
  }

  Widget _buildShimmerSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section label shimmer
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 18.h,
            width: 150.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ),
        buildSizedBoxH(15.h),
        // Options chips shimmer
        _buildShimmerChipsList(context),
      ],
    );
  }

  Widget _buildShimmerChipsList(BuildContext context) {
    return Wrap(
      children: List.generate(
        5, // Generate 5 shimmer chips per section
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w, bottom: 8.h),
          child: _buildShimmerChip(context),
        ),
      ),
    );
  }

  Widget _buildShimmerChip(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 42.h,
        width: 125.w, // Random width for variety
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            buildSizedboxW(12.w),
            // Icon shimmer with preserved size
            Container(
              height: 20.h,
              width: 20.w,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(10.r),
              ),
            ),
            buildSizedboxW(8.w),
            // Text shimmer
            Expanded(
              child: Container(
                height: 14.h,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
            buildSizedboxW(12.w),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerButton(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 55.h,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(100.r),
        ),
      ),
    );
  }
}
