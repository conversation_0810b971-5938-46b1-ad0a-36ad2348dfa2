Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA28AD0000 ntdll.dll
7FFA26C40000 KERNEL32.DLL
7FFA25BC0000 KERNELBASE.dll
7FFA27C00000 USER32.dll
7FFA264A0000 win32u.dll
7FFA278D0000 GDI32.dll
7FFA260F0000 gdi32full.dll
7FFA25FA0000 msvcp_win.dll
7FFA26640000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA288B0000 advapi32.dll
7FFA27590000 msvcrt.dll
7FFA28980000 sechost.dll
7FFA260C0000 bcrypt.dll
7FFA27660000 RPCRT4.dll
7FFA25360000 CRYPTBASE.DLL
7FFA26420000 bcryptPrimitives.dll
7FFA27890000 IMM32.DLL
