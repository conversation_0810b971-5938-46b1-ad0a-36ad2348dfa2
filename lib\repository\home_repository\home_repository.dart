import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/logger.dart';
import 'package:room_eight/models/common_model/common_model.dart';
import 'package:room_eight/models/home_model/block_profile_model.dart';
import 'package:room_eight/models/home_model/detailed_profile_model.dart';
import 'package:room_eight/models/home_model/get_all_profile_model.dart';
import 'package:room_eight/models/home_model/report_profile_model.dart';

class HomeRepository {
  final ApiClient apiClient;
  HomeRepository({required this.apiClient});

  Future<UserDataModel> getAllUserData() async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        ApiEndPoint.getAllUserDataUrl,
      );
      return UserDataModel.fromJson(response);
    } catch (error) {
      Logger.lOG("Problem is in 'getAllUserData' Error : ${error.toString()}");
      rethrow;
    }
  }

  Future<CommonModel> likeUserProfile(Map<String, dynamic> data) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.likeUserProfile,
        data: data,
      );
      return CommonModel.fromJson(response);
    } catch (error) {
      Logger.lOG("Problem is in 'likeUserProfile' Error : ${error.toString()}");
      rethrow;
    }
  }

  Future<CommonModel> dislikeUserProfile(Map<String, dynamic> data) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.dislikeUserProfile,
        data: data,
      );
      return CommonModel.fromJson(response);
    } catch (error) {
      Logger.lOG(
        "Problem is in 'dislikeUserProfile' Error : ${error.toString()}",
      );
      rethrow;
    }
  }

  Future<DetailedProfileModel> getUserProfileById({required int id}) async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        "${ApiEndPoint.getUserProfileById}=$id",
      );
      return DetailedProfileModel.fromJson(response);
    } catch (error) {
      Logger.lOG(
        "Problem is in 'getUserProfileById' Error : ${error.toString()}",
      );
      rethrow;
    }
  }

  Future<BlockProfileModel> blockProfile({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.blocProfile,
        data: data,
      );
      return BlockProfileModel.fromJson(response);
    } catch (error) {
      Logger.lOG("Problem is in 'blockProfile' Error : ${error.toString()}");
      rethrow;
    }
  }

  Future<ReportProfileModel> reportProfile({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.reportProfile,
        data: data,
      );
      return ReportProfileModel.fromJson(response);
    } catch (error) {
      Logger.lOG("Problem is in 'reportProfile' Error : ${error.toString()}");
      rethrow;
    }
  }

}
