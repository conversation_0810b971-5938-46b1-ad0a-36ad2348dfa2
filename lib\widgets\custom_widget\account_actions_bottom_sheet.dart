// widgets/custom_widget/account_actions_bottom_sheet.dart
import 'package:room_eight/core/utils/app_exports.dart';

class AccountActionsBottomSheet {
  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const _AccountActionsContent(),
    );
  }
}

class _AccountActionsContent extends StatelessWidget {
  const _AccountActionsContent();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionTile(
          context,
          icon: Icons.account_balance_wallet_outlined,
          title: 'Deposit',
          action: 'deposit',
        ),
        _buildActionTile(
          context,
          icon: Icons.outbox_outlined,
          title: 'Withdrawal',
          action: 'withdrawal',
        ),
        _buildActionTile(
          context,
          icon: Icons.attach_money_outlined,
          title: 'Account funds',
          action: 'funds',
        ),
        _buildActionTile(
          context,
          icon: Icons.settings_outlined,
          title: 'Account setting',
          action: 'settings',
        ),
        _buildActionTile(
          context,
          icon: Icons.switch_account_outlined,
          title: 'Switch account',
          action: 'switch',
        ),
      ],
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String action,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: () => _onAccountActionSelected(context, action),
    );
  }

  void _onAccountActionSelected(BuildContext context, String action) {
    Navigator.of(context).pop();
    // Example: context.read<TradeBloc>().add(AccountActionEvent(action));
  }
}
