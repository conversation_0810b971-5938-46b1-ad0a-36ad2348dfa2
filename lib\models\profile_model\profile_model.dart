class ProfileModel {
  bool? status;
  String? message;
  SelectionData? data;

  ProfileModel({this.status, this.message, this.data});

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? SelectionData.fromJson(json['data']) : null,
    );
  }
}

class SelectionData {
  List<ProfileOptionModel>? habitsLifestyle;
  List<ProfileOptionModel>? livingStyle;
  List<ProfileOptionModel>? interestsHobbies;

  SelectionData({
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
  });

  factory SelectionData.fromJson(Map<String, dynamic> json) {
    return SelectionData(
      habitsLifestyle: (json['habits_lifestyle'] as List?)
          ?.map((e) => ProfileOptionModel.fromJson(e))
          .toList(),
      livingStyle: (json['living_style'] as List?)
          ?.map((e) => ProfileOptionModel.fromJson(e))
          .toList(),
      interestsHobbies: (json['interests_hobbies'] as List?)
          ?.map((e) => ProfileOptionModel.fromJson(e))
          .toList(),
    );
  }
}

class ProfileOptionModel {
  int? id;
  String? name;
  String? icon;

  ProfileOptionModel({this.id, this.name, this.icon});

  factory ProfileOptionModel.fromJson(Map<String, dynamic> json) {
    return ProfileOptionModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}
