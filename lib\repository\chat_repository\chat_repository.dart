import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/logger.dart';
import 'package:room_eight/views/chat/model/chat_message_list_model.dart';

class ChatRepository {
  final ApiClient apiClient;
  ChatRepository({required this.apiClient});

  Future<ChatMessageListModel> getMessageList({required int chatUserId}) async {
    try {
      var response = await apiClient.request(
        RequestType.GET,
        "${ApiEndPoint.messageListUrl}?chat_user_id=$chatUserId",
      );
      return ChatMessageListModel.fromJson(response);
    } catch (error) {
      Logger.lOG("Problem is in 'getMessageList' Error : ${error.toString()}");
      rethrow;
    }
  }

  // Method expected by ChatBloc
  Future<ChatMessageListModel> getChatMessageListApi(
    String userId,
    int page,
  ) async {
    try {
      Logger.lOG(
        "Making API request to: ${ApiEndPoint.messageListUrl}?chat_user_id=$userId&page=$page",
      );

      var response = await apiClient.request(
        RequestType.GET,
        "${ApiEndPoint.messageListUrl}?chat_user_id=$userId&page=$page",
      );

      Logger.lOG("Raw API response: $response");
      Logger.lOG("Response type: ${response.runtimeType}");

      return ChatMessageListModel.fromJson(response);
    } catch (error, stackTrace) {
      Logger.lOG(
        "Problem is in 'getChatMessageListApi' Error : ${error.toString()}",
      );
      Logger.lOG("Stack trace: $stackTrace");
      rethrow;
    }
  }
}
