import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/onboarding_bloc/onboarding_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  static Widget builder(BuildContext context) {
    return const OnboardingScreen();
  }

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customColors = Theme.of(context).customColors;
    final bgImages = [
      Assets.images.pngs.other.pngOnboardingBg1.path,
      Assets.images.pngs.other.pngOnboardingBg2.path,
    ];

    return BlocListener<OnboardingBloc, OnboardingState>(
      listener: (context, state) {
        if (state is OnboardingNavigateToLogin) {
          NavigatorService.pushAndRemoveUntil(AppRoutes.loginScreen);
        }
      },
      child: BlocBuilder<OnboardingBloc, OnboardingState>(
        builder: (context, state) {
          final currentPage = _getCurrentPage(state);

          // Sync the page controller if needed
          if (_pageController.hasClients &&
              _pageController.page?.round() != currentPage) {
            _pageController.jumpToPage(currentPage);
          }

          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: _getSystemUiOverlayStyle(context),
            child: Scaffold(
              backgroundColor: customColors.fillColor,
              body: BackgroundImage(
                imagePath: bgImages[currentPage],
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                        top: 60.h,
                        bottom: 16.h,
                      ),
                      child: _buildTopBar(context),
                    ),
                    Expanded(child: _buildPageView(context, currentPage)),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPageView(BuildContext context, int currentPage) {
    return PageView(
      controller: _pageController,
      onPageChanged: (index) {
        context.read<OnboardingBloc>().add(OnboardingPageChanged(index));
      },
      children: [
        _buildOnboardingPage(
          context,
          Lang.of(context).msg_onboarding_title,
          Lang.of(context).msg_onboarding_description,
          0,
        ),
        _buildOnboardingPage(
          context,
          Lang.of(context).msg_onboarding_title2,
          Lang.of(context).msg_onboarding_description2,
          1,
        ),
      ],
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    final topColor = customColors.fillColor!.withAlpha((0.6 * 255).toInt());
    final bottomColor = customColors.blackColor!.withAlpha((0.4 * 255).toInt());
    final fillColor = customColors.fillColor!.withAlpha(75);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomGradientContainer(
          onTap: () => context.read<OnboardingBloc>().add(OnboardingSkip()),
          height: 36.h,
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          topColor: topColor,
          bottomColor: bottomColor,
          fillColor: fillColor,
          child: Text(
            Lang.of(context).lbl_skip,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: customColors.fillColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: topColor,
          bottomColor: bottomColor,
          fillColor: fillColor,
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icForwardArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildOnboardingPage(
    BuildContext context,
    String title,
    String description,
    int pageIndex,
  ) {
    final customColors = Theme.of(context).customColors;
    final textTheme = Theme.of(context).textTheme;
    _getCurrentPage(context.read<OnboardingBloc>().state);

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              CustomImageView(
                imagePath: Assets.images.pngs.other.pngOnboardingContainer.path,
                fit: BoxFit.contain,
              ),
              Positioned.fill(
                bottom: 0,
                right: 0,
                left: 0,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        title,
                        textAlign: TextAlign.start,
                        style: textTheme.headlineSmall?.copyWith(
                          color: customColors.blackColor,
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      buildSizedBoxH(8.h),
                      Text(
                        description,
                        textAlign: TextAlign.start,
                        style: textTheme.bodyMedium?.copyWith(
                          color: customColors.darkGreytextcolor,
                        ),
                      ),
                      buildSizedBoxH(8.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomElevatedButton(
                            text: Lang.of(context).lbl_continue,
                            width: MediaQuery.of(context).size.width * 0.4,
                            onPressed: () {
                              if (pageIndex < 1) {
                                _pageController.nextPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                                context.read<OnboardingBloc>().add(
                                  OnboardingPageChanged(pageIndex + 1),
                                );
                              } else {
                                context.read<OnboardingBloc>().add(
                                  OnboardingContinue(),
                                );
                              }
                            },
                          ),
                          _buildPageIndicator(context),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPageIndicator(BuildContext context) {
    final customColors = Theme.of(context).customColors;
    _getCurrentPage(context.read<OnboardingBloc>().state);

    return SmoothPageIndicator(
      controller: _pageController,
      count: 2,
      effect: CustomizableEffect(
        activeDotDecoration: DotDecoration(
          width: 28.w,
          height: 8.h,
          color: customColors.primaryColor!,
          borderRadius: BorderRadius.circular(8.h),
        ),
        dotDecoration: DotDecoration(
          width: 8.w,
          height: 8.h,
          color: customColors.fillColor!,
          borderRadius: BorderRadius.circular(8.h),
        ),
        spacing: 4.w,
      ),
    );
  }

  SystemUiOverlayStyle _getSystemUiOverlayStyle(BuildContext context) {
    return const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
    );
  }

  int _getCurrentPage(OnboardingState state) {
    if (state is OnboardingPageUpdated) {
      return state.currentPage;
    }
    if (state is OnboardingInitial) {
      return state.currentPage;
    }
    return 0;
  }
}
