class GetLikeProfileInwardModel {
  bool? status;
  String? message;
  List<LikeAndDisLikeUsers>? data;

  GetLikeProfileInwardModel({this.status, this.message, this.data});

  factory GetLikeProfileInwardModel.fromJson(Map<String, dynamic> json) {
    return GetLikeProfileInwardModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? List<LikeAndDisLikeUsers>.from(
              json['data'].map((item) => LikeAndDisLikeUsers.fromJson(item)),
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }
}

class LikeAndDisLikeUsers {
  int? id;
  String? name;
  String? profileImage;
  String? about;
  String? year;
  bool? status;

  LikeAndDisLikeUsers({
    this.id,
    this.name,
    this.profileImage,
    this.about,
    this.year,
    this.status,
  });

  factory LikeAndDisLikeUsers.fromJson(Map<String, dynamic> json) {
    return LikeAndDisLikeUsers(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
      about: json['about'] ?? '',
      year: json['year'] ?? '',
      status: json['status'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'profile_image': profileImage,
      'about': about,
      'year': year,
      'status': status,
    };
  }
}
