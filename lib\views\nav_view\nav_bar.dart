import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/views/home_view/home_screen.dart';
import 'package:room_eight/views/like_view/like_screen.dart';
import 'package:room_eight/views/profile_view/profile_screen.dart';
import 'package:room_eight/viewmodels/home_bloc/home_bloc.dart';
import 'package:room_eight/views/setting_view/setting_screen.dart'; // Add this import

class RoomEightNavBar extends StatefulWidget {
  const RoomEightNavBar({super.key});

  static Widget builder(BuildContext context) => const RoomEightNavBar();

  @override
  State<RoomEightNavBar> createState() => _RoomEightNavBarState();
}

class _RoomEightNavBarState extends State<RoomEightNavBar> {
  late PersistentTabController _controller;
  bool _showSplash = true;

  @override
  void initState() {
    super.initState();
    final navState = context.read<NavBloc>().state;
    context.read<HomeBloc>().add(GetAllProfile());
    _controller = PersistentTabController(initialIndex: navState.selectedIndex);
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showSplash = false;
          //set this in shareprefrenc of it come only one time
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<NavBloc, NavState>(
      listenWhen: (previous, current) =>
          previous.selectedIndex != current.selectedIndex,
      listener: (context, state) {
        if (_controller.index != state.selectedIndex) {
          _controller.index = state.selectedIndex;
        }
      },
      child: BlocBuilder<NavBloc, NavState>(
        builder: (context, state) {
          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle(
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarIconBrightness: Brightness.light,
              systemNavigationBarColor: Theme.of(
                context,
              ).customColors.blackColor,
              // systemNavigationBarColor: Colors.transparent,
            ),
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              extendBody: true,
              body: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(bottom: 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100.r),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).customColors.blackColor!,
                          blurRadius: 10.r,
                        ),
                      ],
                    ),
                    child: PersistentTabView(
                      // navBarHeight: kBottomNavigationBarHeight,\
                      navBarHeight: 70.h,
                      padding: EdgeInsets.zero,
                      context,
                      controller: _controller,
                      screens: _buildScreens(),
                      items: _navBarsItems(context, state),
                      confineToSafeArea: true,
                      backgroundColor: Theme.of(
                        context,
                      ).customColors.primaryColor!,
                      handleAndroidBackButtonPress: true,
                      resizeToAvoidBottomInset: true,
                      stateManagement: true,
                      decoration: NavBarDecoration(
                        borderRadius: BorderRadius.circular(100.r),
                        useBackdropFilter: false,

                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(
                              context,
                            ).customColors.blackColor!.withValues(alpha: 0.2),
                            blurRadius: 20.r,
                          ),
                        ],
                      ),
                      margin: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                        bottom: Platform.isIOS ? 16.h : 10.h,
                      ),
                      navBarStyle: NavBarStyle.simple,
                      onItemSelected: (index) {
                        context.read<NavBloc>().add(NavTabChanged(index));
                      },
                    ),
                  ),
                  if (_showSplash)
                    GestureDetector(
                      onTap: () => setState(() {
                        _showSplash = false;
                      }),
                      child: Container(
                        color: Colors.black.withValues(alpha: 0.45),
                        child: Center(
                          child: CustomImageView(
                            imagePath: Assets.lottie.icArrow.path,
                            height: 50.h,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildScreens() {
    return [const HomeScreen(), LikeScreen(), ProfileScreen(), SettingScreen()];
  }

  List<PersistentBottomNavBarItem> _navBarsItems(
    BuildContext context,
    NavState state,
  ) {
    return [
      PersistentBottomNavBarItem(
        onSelectedTabPressWhenNoScreensPushed: () {
          Navigator.of(context).popUntil((route) => route.isFirst);
          // Only reset to first when tapping home tab while already on home screen
          if (state.selectedIndex == 0) {
            context.read<HomeBloc>().add(const HomeResetToFirst());
          }
        },
        icon: _buildNavBarIcon(
          unselectedIcon: Assets.images.svgs.icons.icHome.path,
          selectedIcon: Assets.images.svgs.icons.icHomeFill.path,
          index: 0,
          isSelected: state.selectedIndex == 0,

          context: context,

          onTap: () {
            // Only reset to first if we're already on home tab and tapping again
            if (state.selectedIndex == 0) {
              context.read<HomeBloc>().add(const HomeResetToFirst());
            } else {
              // Just change tab without resetting
              context.read<NavBloc>().add(NavTabChanged(0));
            }
          },
        ),
        scrollToTopOnNavBarItemPress: true,
      ),
      PersistentBottomNavBarItem(
        icon: _buildNavBarIcon(
          unselectedIcon: Assets.images.svgs.icons.icHeart.path,
          selectedIcon: Assets.images.svgs.icons.icHeartFill.path,
          index: 1,
          isSelected: state.selectedIndex == 1,
          context: context,
          onTap: () {
            context.read<NavBloc>().add(NavTabChanged(1));
          },
        ),
        scrollToTopOnNavBarItemPress: true,
      ),

      PersistentBottomNavBarItem(
        icon: _buildNavBarIcon(
          unselectedIcon: Assets.images.svgs.icons.icPerson.path,
          selectedIcon: Assets.images.svgs.icons.icPersonFill.path,
          index: 2,
          isSelected: state.selectedIndex == 2,
          context: context,
          onTap: () {
            // Only reset to first if we're already on home tab and tapping again
            if (state.selectedIndex == 2) {
            } else {
              // Just change tab without resetting
              context.read<ProfileBloc>().add(GetSelectionOption());
              context.read<ProfileBloc>().add(GetUserProfile());
              context.read<NavBloc>().add(NavTabChanged(2));
            }
          },
        ),
        scrollToTopOnNavBarItemPress: true,
      ),
      PersistentBottomNavBarItem(
        icon: _buildNavBarIcon(
          unselectedIcon: Assets.images.svgs.icons.icSetting1.path,
          selectedIcon: Assets.images.svgs.icons.icSetting1Fill.path,
          index: 3,
          isSelected: state.selectedIndex == 3,
          context: context,
          onTap: () {
            context.read<NavBloc>().add(NavTabChanged(3));
          },
        ),
        scrollToTopOnNavBarItemPress: true,
      ),
    ];
  }

  Widget _buildNavBarIcon({
    required String unselectedIcon,
    required String selectedIcon,
    required int index,
    required bool isSelected,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 50.h,
            width: 50.w,
            padding: EdgeInsets.all(0.w),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).customColors.fillColor
                  : Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: CustomImageView(
              margin: EdgeInsets.all(10.0),
              imagePath: isSelected ? selectedIcon : unselectedIcon,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).customColors.fillColor,
            ),
          ),
        ],
      ),
    );
  }
}
