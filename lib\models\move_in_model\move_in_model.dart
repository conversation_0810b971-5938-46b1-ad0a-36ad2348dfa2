class MoveInModel {
  bool? status;
  String? message;
  List<MoveInUser>? data;

  MoveInModel({this.status, this.message, this.data});

  factory MoveInModel.fromJson(Map<String, dynamic> json) {
    return MoveInModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? List<MoveInUser>.from(
              json['data'].map((item) => MoveInUser.fromJson(item)),
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }
}

class MoveInUser {
  int? id;
  String? name;
  String? year;
  bool? status;
  String? profileImage;
  String? about;
  int? toMessageId;

  MoveInUser({
    this.id,
    this.name,
    this.year,
    this.status,
    this.profileImage,
    this.about,
    this.toMessageId,
  });

  factory MoveInUser.from<PERSON>son(Map<String, dynamic> json) {
    return MoveInUser(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      year: json['year']?.toString() ?? '',
      status: json['status'] ?? false,
      profileImage: json['profile_image'] ?? '',
      about: json['about'] ?? '',
      toMessageId: json['to_message_id'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'year': year,
      'status': status,
      'profile_image': profileImage,
      'about': about,
      'to_message_id': toMessageId,
    };
  }
}
