import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'nav_event.dart';
part 'nav_state.dart';

class NavBloc extends Bloc<NavEvent, NavState> {
  NavBloc() : super(const NavState(selectedIndex: 0)) {
    on<NavTabChanged>(_onTabChanged);
  }

  void _onTabChanged(NavTabChanged event, Emitter<NavState> emit) {
    emit(state.copyWith(selectedIndex: event.index));
  }
}
