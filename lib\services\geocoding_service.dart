import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:room_eight/models/location_model/location_model.dart';
import 'package:room_eight/core/utils/logger.dart';

class GeocodingService {
  static const int _limit = 10;
  static const String _baseUrl = 'https://nominatim.openstreetmap.org';

  /// Get current location of the user
  static Future<LocationModel?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Logger.lOG('Location services are disabled.');
        return null;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Logger.lOG('Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Logger.lOG('Location permissions are permanently denied');
        return null;
      }

      // Get current position with updated settings
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      return LocationModel(
        name: 'Current Location',
        latitude: position.latitude,
        longitude: position.longitude,
        address:
            'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}',
      );
    } catch (e) {
      Logger.lOG('Error getting current location: $e');
      return null;
    }
  }

  /// Search for locations using OpenStreetMap Nominatim API
  /// This provides real-time search for any location worldwide
  static Future<List<LocationModel>> searchLocations(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      final encodedQuery = Uri.encodeComponent(query.trim());

      // Enhanced search with detailed parameters - WORLDWIDE search (removed countrycodes restriction)
      final url = Uri.parse(
        '$_baseUrl/search?q=$encodedQuery&format=json&limit=15&addressdetails=1&extratags=1&namedetails=1&dedupe=1',
      );

      final response = await http.get(
        url,
        headers: {
          'User-Agent': 'RoomEightApp/1.0 (Flutter Mobile App)',
          'Accept': 'application/json',
          'Accept-Language':
              'en', // Support multiple languages for worldwide search
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        // Filter and sort results for better relevance
        final filteredResults = data.where((item) {
          final importance =
              double.tryParse(item['importance']?.toString() ?? '0') ?? 0.0;
          final type = item['type']?.toString() ?? '';
          final osmType = item['osm_type']?.toString() ?? '';

          // Include comprehensive location types for worldwide search
          final validTypes = [
            // Buildings and addresses
            'house', 'building', 'residential', 'commercial', 'apartment',
            'hotel', 'office', 'retail', 'industrial',

            // Roads and transportation
            'road', 'street', 'path', 'way', 'highway', 'avenue', 'boulevard',
            'lane', 'drive', 'circle', 'court', 'place',

            // Areas and neighborhoods
            'suburb', 'neighbourhood', 'quarter', 'district', 'ward',
            'precinct', 'zone', 'area', 'locality', 'sublocality',

            // Administrative divisions
            'city', 'town', 'village', 'municipality', 'borough', 'county',
            'state', 'province', 'region', 'country', 'territory',

            // Postal and geographic
            'postcode', 'postal_code', 'zip_code',

            // Points of interest
            'amenity', 'shop', 'tourism', 'leisure', 'historic',
            'natural', 'landuse', 'place_of_worship',

            // Educational and medical
            'university', 'school', 'hospital', 'clinic',

            // Transportation hubs
            'airport', 'station', 'stop', 'terminal',
          ];

          // More lenient filtering for worldwide search
          return importance > 0.05 ||
              validTypes.contains(type) ||
              osmType == 'way' ||
              osmType == 'node' ||
              osmType == 'relation';
        }).toList();

        // Sort by importance and relevance
        filteredResults.sort((a, b) {
          final importanceA =
              double.tryParse(a['importance']?.toString() ?? '0') ?? 0.0;
          final importanceB =
              double.tryParse(b['importance']?.toString() ?? '0') ?? 0.0;
          return importanceB.compareTo(importanceA);
        });

        return filteredResults.take(_limit).map((item) {
          final displayName = item['display_name'] ?? '';
          final lat = double.tryParse(item['lat']?.toString() ?? '0') ?? 0.0;
          final lon = double.tryParse(item['lon']?.toString() ?? '0') ?? 0.0;

          // Extract a shorter, more readable name
          String name = _extractLocationName(item);

          return LocationModel(
            name: name,
            latitude: lat,
            longitude: lon,
            address: displayName,
          );
        }).toList();
      } else {
        Logger.lOG('Geocoding API error: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.lOG('Geocoding service error: $e');
      return [];
    }
  }

  /// Extract a readable location name from the API response
  /// Enhanced to handle worldwide addresses with streets, areas, and districts
  static String _extractLocationName(Map<String, dynamic> item) {
    final address = item['address'] as Map<String, dynamic>?;
    final displayName = item['display_name'] ?? '';
    final type = item['type']?.toString() ?? '';

    if (address != null) {
      // For street-level searches, prioritize street/road information
      final road = address['road'] ?? address['street'] ?? address['highway'];
      final houseNumber = address['house_number'];
      final suburb =
          address['suburb'] ??
          address['neighbourhood'] ??
          address['quarter'] ??
          address['residential'] ??
          address['locality'] ??
          address['sublocality'];
      final district =
          address['state_district'] ??
          address['district'] ??
          address['county'] ??
          address['region'];
      final city =
          address['city'] ??
          address['town'] ??
          address['village'] ??
          address['municipality'] ??
          address['borough'];
      final state = address['state'] ?? address['province'];
      final country = address['country'];

      // Build name based on the type of location found
      if (type == 'road' || type == 'street' || type == 'way') {
        // For roads/streets, show: "Street Name, Area, City"
        if (road != null && suburb != null && city != null) {
          return '$road, $suburb, $city';
        } else if (road != null && city != null) {
          return '$road, $city';
        } else if (road != null) {
          return road;
        }
      }

      if (type == 'house' || type == 'building') {
        // For buildings, show: "House# Street, Area, City"
        if (houseNumber != null && road != null && suburb != null) {
          return '$houseNumber $road, $suburb';
        } else if (road != null && suburb != null) {
          return '$road, $suburb';
        }
      }

      if (type == 'suburb' || type == 'neighbourhood' || type == 'quarter') {
        // For areas/suburbs, show: "Area, City, State"
        if (suburb != null && city != null && state != null) {
          return '$suburb, $city, $state';
        } else if (suburb != null && city != null) {
          return '$suburb, $city';
        }
      }

      // Enhanced fallback for worldwide locations - include country when helpful
      if (city != null && state != null && country != null) {
        // For international locations, include country
        if (country != 'India' && country != 'United States') {
          return '$city, $state, $country';
        } else {
          return '$city, $state';
        }
      } else if (city != null && country != null) {
        // For cities without state info, include country for international locations
        if (country != 'India' && country != 'United States') {
          return '$city, $country';
        } else {
          return city;
        }
      } else if (city != null) {
        return city;
      } else if (suburb != null && state != null) {
        return '$suburb, $state';
      } else if (suburb != null && country != null) {
        if (country != 'India' && country != 'United States') {
          return '$suburb, $country';
        } else {
          return suburb;
        }
      } else if (suburb != null) {
        return suburb;
      } else if (district != null && state != null) {
        return '$district, $state';
      } else if (state != null && country != null) {
        if (country != 'India' && country != 'United States') {
          return '$state, $country';
        } else {
          return state;
        }
      } else if (country != null) {
        return country;
      }
    }

    // Enhanced fallback: try to extract meaningful parts from display_name
    final parts = displayName.split(',').map((part) => part.trim()).toList();
    if (parts.length >= 3) {
      // For detailed addresses, show first 3 parts
      return '${parts[0]}, ${parts[1]}, ${parts[2]}';
    } else if (parts.length >= 2) {
      // For medium addresses, show first 2 parts
      return '${parts[0]}, ${parts[1]}';
    } else if (parts.isNotEmpty) {
      return parts.first;
    }

    return displayName;
  }

  /// Enhanced search method that tries multiple search strategies
  /// for better results with detailed addresses like streets and areas
  static Future<List<LocationModel>> searchLocationsEnhanced(
    String query,
  ) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      // First, try the standard search
      List<LocationModel> results = await searchLocations(query);

      // If we get good results, return them
      if (results.isNotEmpty && results.length >= 3) {
        return results;
      }

      // If limited results, try alternative search strategies
      final queryParts = query.split(',').map((part) => part.trim()).toList();

      if (queryParts.length > 1) {
        // Try searching with different combinations
        final alternativeQueries = <String>[];

        // Try last part first (often the city/state)
        if (queryParts.length >= 2) {
          alternativeQueries.add(
            '${queryParts.last}, ${queryParts[queryParts.length - 2]}',
          );
        }

        // Try first and last parts
        if (queryParts.length >= 3) {
          alternativeQueries.add('${queryParts.first}, ${queryParts.last}');
        }

        // Try just the city/area name
        if (queryParts.length >= 2) {
          alternativeQueries.add(queryParts[queryParts.length - 2]);
        }

        for (final altQuery in alternativeQueries) {
          final altResults = await searchLocations(altQuery);
          if (altResults.isNotEmpty) {
            // Merge results, avoiding duplicates
            final existingNames = results
                .map((r) => r.name.toLowerCase())
                .toSet();
            final newResults = altResults
                .where((r) => !existingNames.contains(r.name.toLowerCase()))
                .toList();
            results.addAll(newResults);

            if (results.length >= 5) break;
          }
        }
      }

      return results.take(10).toList();
    } catch (e) {
      Logger.lOG('Enhanced geocoding search error: $e');
      return [];
    }
  }

  /// Get popular/suggested locations for initial display
  static Future<List<LocationModel>> getPopularLocations() async {
    // Return popular worldwide cities as initial suggestions
    final popularQueries = [
      // Major Indian cities
      'Mumbai, Maharashtra, India',
      'Delhi, India',
      'Bangalore, Karnataka, India',
      'Surat, Gujarat, India',
      'Pune, Maharashtra, India',

      // Major international cities
      'New York, United States',
      'London, United Kingdom',
      'Tokyo, Japan',
      'Paris, France',
      'Dubai, UAE',
      'Singapore',
      'Sydney, Australia',
      'Toronto, Canada',
    ];

    List<LocationModel> popularLocations = [];

    for (final query in popularQueries) {
      try {
        final results = await searchLocations(query);
        if (results.isNotEmpty) {
          popularLocations.add(results.first);
        }
        if (popularLocations.length >= 8) break;
      } catch (e) {
        Logger.lOG('Error fetching popular location for $query: $e');
      }
    }

    return popularLocations;
  }

  /// Search with multiple strategies for better worldwide results
  static Future<List<LocationModel>> searchWithFallback(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      // Strategy 1: Direct search
      List<LocationModel> results = await searchLocations(query);

      if (results.isNotEmpty) {
        return results;
      }

      // Strategy 2: Try with common location patterns
      final fallbackQueries = <String>[];

      // Add "city" if it looks like a city name
      if (!query.toLowerCase().contains('city') &&
          !query.toLowerCase().contains('town') &&
          !query.toLowerCase().contains('village')) {
        fallbackQueries.add('$query city');
      }

      // Try with common country suffixes if no country is specified
      if (!query.toLowerCase().contains('usa') &&
          !query.toLowerCase().contains('united states') &&
          !query.toLowerCase().contains('india') &&
          !query.toLowerCase().contains('uk') &&
          !query.toLowerCase().contains('canada')) {
        fallbackQueries.addAll([
          '$query, USA',
          '$query, India',
          '$query, UK',
          '$query, Canada',
        ]);
      }

      // Try each fallback query
      for (final fallbackQuery in fallbackQueries) {
        final fallbackResults = await searchLocations(fallbackQuery);
        if (fallbackResults.isNotEmpty) {
          results.addAll(fallbackResults);
          if (results.length >= 5) break;
        }
      }

      return results.take(10).toList();
    } catch (e) {
      Logger.lOG('Fallback search error: $e');
      return [];
    }
  }
}
