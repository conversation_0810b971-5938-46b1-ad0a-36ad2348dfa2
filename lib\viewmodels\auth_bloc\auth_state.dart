part of 'auth_bloc.dart';

class AuthState extends Equatable {
  final String phoneNumber;
  final GlobalKey<FormState> loginFormKey;
  final GlobalKey<FormState> signupFormKey;
  final GlobalKey<FormState> addPersonalDetailFormKey;
  final bool isloginLoading;
  final bool isSignupLoading;
  final TextEditingController? phoneController;
  final FocusNode? phonefocusnode;
  final TextEditingController? emailController;
  final TextEditingController? passwordController;
  final FocusNode? emailFocusNode;
  final FocusNode? passwordFocusNode;
  final bool obscurePassword;
  final bool signupObscurePassword;
  final bool signupObscureConfirmPassword;
  final bool isAgreementAccepted;
  final TextEditingController? signupNameController;
  final FocusNode? signupNamefocusnode;
  final TextEditingController? signupEmailController;
  final FocusNode? signupEmailfocusnode;
  final TextEditingController? signupPasswordController;
  final FocusNode? signupPasswordfocusnode;
  final TextEditingController? signupConfirmPasswordController;
  final FocusNode? signupConfirmPasswordfocusnode;

  const AuthState({
    this.phoneNumber = '',
    required this.loginFormKey,
    required this.signupFormKey,
    required this.addPersonalDetailFormKey,
    this.isloginLoading = false,
    this.isSignupLoading = false,
    this.phoneController,
    this.phonefocusnode,
    this.emailController,
    this.passwordController,
    this.emailFocusNode,
    this.passwordFocusNode,
    this.obscurePassword = true,
    this.isAgreementAccepted = false,
    this.signupNameController,
    this.signupNamefocusnode,
    this.signupEmailController,
    this.signupEmailfocusnode,
    this.signupPasswordController,
    this.signupPasswordfocusnode,
    this.signupConfirmPasswordController,
    this.signupConfirmPasswordfocusnode,
    this.signupObscurePassword = true,
    this.signupObscureConfirmPassword = true,
  });

  @override
  List<Object?> get props => [
    phoneNumber,
    loginFormKey,
    signupFormKey,
    addPersonalDetailFormKey,
    isloginLoading,
    isSignupLoading,
    phoneController,
    phonefocusnode,
    emailController,
    passwordController,
    emailFocusNode,
    passwordFocusNode,
    obscurePassword,
    isAgreementAccepted,
    signupNameController,
    signupNamefocusnode,
    signupEmailController,
    signupEmailfocusnode,
    signupPasswordController,
    signupPasswordfocusnode,
    signupConfirmPasswordController,
    signupConfirmPasswordfocusnode,
    signupObscurePassword,
    signupObscureConfirmPassword,
  ];

  AuthState copyWith({
    String? phoneNumber,
    GlobalKey<FormState>? loginFormKey,
    GlobalKey<FormState>? signupFormKey,
    GlobalKey<FormState>? addPersonalDetailFormKey,
    bool? isloginLoading,
    bool? isSignupLoading,
    TextEditingController? phoneController,
    FocusNode? phonefocusnode,
    TextEditingController? emailController,
    TextEditingController? passwordController,
    FocusNode? emailFocusNode,
    FocusNode? passwordFocusNode,
    bool? obscurePassword,
    bool? isAgreementAccepted,
    TextEditingController? signupNameController,
    FocusNode? signupNamefocusnode,
    TextEditingController? signupEmailController,
    FocusNode? signupEmailfocusnode,
    TextEditingController? signupPasswordController,
    FocusNode? signupPasswordfocusnode,
    TextEditingController? signupConfirmPasswordController,
    FocusNode? signupConfirmPasswordfocusnode,
    final bool? signupObscurePassword,
    final bool? signupObscureConfirmPassword,
  }) {
    return AuthState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      loginFormKey: loginFormKey ?? this.loginFormKey,
      signupFormKey: signupFormKey ?? this.signupFormKey,
      addPersonalDetailFormKey:
          addPersonalDetailFormKey ?? this.addPersonalDetailFormKey,
      isloginLoading: isloginLoading ?? this.isloginLoading,
      isSignupLoading: isSignupLoading ?? this.isSignupLoading,
      phoneController: phoneController ?? this.phoneController,
      phonefocusnode: phonefocusnode ?? this.phonefocusnode,
      emailController: emailController ?? this.emailController,
      passwordController: passwordController ?? this.passwordController,
      emailFocusNode: emailFocusNode ?? this.emailFocusNode,
      passwordFocusNode: passwordFocusNode ?? this.passwordFocusNode,
      obscurePassword: obscurePassword ?? this.obscurePassword,
      isAgreementAccepted: isAgreementAccepted ?? this.isAgreementAccepted,
      signupNameController: signupNameController ?? this.signupNameController,
      signupNamefocusnode: signupNamefocusnode ?? this.signupNamefocusnode,
      signupEmailController:
          signupEmailController ?? this.signupEmailController,
      signupEmailfocusnode: signupEmailfocusnode ?? this.signupEmailfocusnode,
      signupPasswordController:
          signupPasswordController ?? this.signupPasswordController,
      signupPasswordfocusnode:
          signupPasswordfocusnode ?? this.signupPasswordfocusnode,
      signupConfirmPasswordController:
          signupConfirmPasswordController ??
          this.signupConfirmPasswordController,
      signupConfirmPasswordfocusnode:
          signupConfirmPasswordfocusnode ?? this.signupConfirmPasswordfocusnode,
      signupObscureConfirmPassword:
          signupObscureConfirmPassword ?? this.signupObscureConfirmPassword,
      signupObscurePassword:
          signupObscurePassword ?? this.signupObscurePassword,
    );
  }
}
