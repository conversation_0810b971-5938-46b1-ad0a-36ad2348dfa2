import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  static Widget builder(BuildContext context) => const WelcomeScreen();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: _buildBody(context, state),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, AuthState state) {
    return BackgroundImage(
      imagePath: Assets.images.pngs.other.pngSignupBg.path,
      child: Column(
        children: [
          _buildTopSection(context),
          Expanded(child: _buildSignupForm(context, state)),
        ],
      ),
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 32.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return SizedBox(
      width: double.infinity,
      height: 40.h,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: 2,
            child: CustomGradientContainer(
              height: 36.w,
              width: 36.w,
              topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
              bottomColor: customColors.blackColor!.withAlpha(
                (0.4 * 255).toInt(),
              ),
              fillColor: customColors.fillColor!.withAlpha(75),
              child: CustomImageView(
                imagePath: Assets.images.svgs.icons.icBackArrow.path,
              ),
            ),
          ),
          Center(
            child: CustomImageView(
              height: 36.h,
              imagePath: Assets.images.pngs.other.pngAppLogo.path,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignupForm(BuildContext context, AuthState state) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).customColors.fillColor?.withValues(alpha: 0.8),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                _buildSignupTitle(context),
                buildSizedBoxH(24.h),
                Expanded(child: _buildScrollableValueCards(context, state)),
                buildSizedBoxH(16.h),
                _buildSignupButton(context, state),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScrollableValueCards(BuildContext context, AuthState state) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          _buildValueCard(
            context: context,
            icon: Assets.images.svgs.icons.icKeepItReal.path,
            title: Lang.of(context).lbl_keep_it_real,
            description: Lang.of(context).lbl_keep_it_real_desc,
          ),
          buildSizedBoxH(8.h),
          _buildValueCard(
            context: context,
            icon: Assets.images.svgs.icons.icSecurity.path,
            title: Lang.of(context).lbl_safety_first,
            description: Lang.of(context).lbl_safety_first_desc,
            linkText: Lang.of(context).lbl_read_safety_tips,
          ),
          buildSizedBoxH(8.h),
          _buildValueCard(
            context: context,
            icon: Assets.images.svgs.icons.icBeKind.path,
            title: Lang.of(context).lbl_be_kind,
            description: Lang.of(context).lbl_be_kind_desc,
          ),
          buildSizedBoxH(8.h),
          _buildValueCard(
            context: context,
            icon: Assets.images.svgs.icons.icSearch.path,
            title: Lang.of(context).lbl_stay_aware,
            description: Lang.of(context).lbl_stay_aware_desc,
          ),
          buildSizedBoxH(8.h),
          _buildValueCard(
            context: context,
            icon: Assets.images.svgs.icons.icPrivacyAndPolicy.path,
            title: Lang.of(context).lbl_privacy_terms,
            description: Lang.of(context).lbl_privacy_terms_desc,
          ),
          buildSizedBoxH(8.h),
          _buildAgreementCheckbox(context, state),
          buildSizedBoxH(8.h),
        ],
      ),
    );
  }

  Widget _buildAgreementCheckbox(BuildContext context, AuthState state) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Checkbox(
          value: state.isAgreementAccepted,
          onChanged: (bool? value) {
            // Add your checkbox toggle logic here
            context.read<AuthBloc>().add(ToggleAgreementAccepted());
          },
          activeColor: Theme.of(context).customColors.primaryColor,
          checkColor: Theme.of(context).customColors.fillColor,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              // Toggle checkbox when text is tapped
              // context.read<AuthBloc>().add(ToggleAgreementAccepted(!(state.isAgreementAccepted ?? false)));
            },
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 14.sp,
                  color: Theme.of(context).customColors.darkGreytextcolor,
                  height: 1.5,
                ),
                children: [
                  TextSpan(text: Lang.of(context).lbl_i_agree_to_the),
                  TextSpan(
                    text: Lang.of(context).lbl_terms_of_service,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      color: Theme.of(context).customColors.primaryColor,
                      fontSize: 14.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: Theme.of(
                        context,
                      ).customColors.primaryColor,
                    ),
                  ),
                  TextSpan(text: Lang.of(context).lbl_and),
                  TextSpan(
                    text: Lang.of(context).lbl_privacy_policy,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      color: Theme.of(context).customColors.primaryColor,
                      fontSize: 14.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: Theme.of(
                        context,
                      ).customColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignupTitle(BuildContext context) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_welcome_message,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 22.sp,
            color: Theme.of(context).customColors.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        buildSizedBoxH(4.h),
        Text(
          Lang.of(context).lbl_welcome_message_desc,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.sp,
            color: Theme.of(context).customColors.darkGreytextcolor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSignupButton(BuildContext context, AuthState state) {
    return CustomElevatedButton(
      isLoading: state.isSignupLoading,
      isDisabled: state.isSignupLoading || !(state.isAgreementAccepted),
      text: Lang.of(context).lbl_agree_and_continue,
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        NavigatorService.pushNamed(AppRoutes.addPersonalDetailScreen);
        // FocusManager.instance.primaryFocus?.unfocus();
        // if ((state.isAgreementAccepted ?? false) &&
        //     (state.signupFormKey.currentState?.validate() ?? false)) {
        //   context.read<AuthBloc>().add(SignupSubmitted());
        // }
      },
    );
  }

  Widget _buildValueCard({
    required BuildContext context,
    required String icon,
    required String title,
    required String description,
    String? linkText,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomImageView(width: 24.w, height: 24.h, imagePath: icon),
          buildSizedboxW(16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                buildSizedBoxH(8),
                RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 14,
                      color: Theme.of(context).customColors.darkGreytextcolor,
                      height: 1.5,
                    ),
                    children: [
                      TextSpan(text: description),
                      if (linkText != null) ...[
                        const TextSpan(text: ' '),
                        TextSpan(
                          text: linkText,
                          style: Theme.of(context).textTheme.bodyLarge!
                              .copyWith(
                                color: Theme.of(
                                  context,
                                ).customColors.primaryColor,
                                decoration: TextDecoration.underline,
                                decorationColor: Theme.of(
                                  context,
                                ).customColors.primaryColor,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
