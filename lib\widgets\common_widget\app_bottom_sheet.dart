import 'package:room_eight/core/utils/app_exports.dart';

class CustomBottomSheetWidget extends StatelessWidget {
  const CustomBottomSheetWidget({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      // padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 20.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildSizedBoxH(8.h),
          Container(
            height: 6.h,
            width: 60.w,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).customColors.primaryColor!.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(100.r),
            ),
          ),
          buildSizedBoxH(12.h),
          child,
        ],
      ),
    );
  }
}
