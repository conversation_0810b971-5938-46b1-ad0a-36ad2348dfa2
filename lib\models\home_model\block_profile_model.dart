class BlockProfileModel {
  final bool status;
  final String message;

  BlockProfileModel({required this.status, required this.message});

  factory BlockProfileModel.fromJson(Map<String, dynamic> json) {
    return BlockProfileModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}
