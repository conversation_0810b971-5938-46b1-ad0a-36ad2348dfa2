class UserDataModel {
  final bool? status;
  final String? message;
  final List<UserData>? users;

  UserDataModel({this.status, this.message, this.users});

  factory UserDataModel.fromJson(Map<String, dynamic> json) {
    return UserDataModel(
      status: json['status'],
      message: json['message'],
      users: (json['data'] as List<dynamic>?)
          ?.map((item) => UserData.fromJson(item))
          .toList(),
    );
  }
}

class UserData {
  final int? id;
  final String? name;
  final String? profileImage;
  final String? preferedSmoking;
  final String? cleaniness;
  final String? preferedLeasePeriod;
  final bool? pets;
  final String? classStanding;

  UserData({
    this.id,
    this.name,
    this.profileImage,
    this.preferedSmoking,
    this.cleaniness,
    this.preferedLeasePeriod,
    this.pets,
    this.classStanding,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
      preferedSmoking: json['prefered_smoking'] ?? '',
      cleaniness: json['cleaniness'] ?? '',
      preferedLeasePeriod: json['prefered_lease_period'] ?? '',
      pets: json['pets'] ?? false,
      classStanding: json['class_standing'] ?? '',
    );
  }
}

// class UserData {
//   final String id;
//   final String name;
//   final String imagePath;
//   final bool isNewHere;
//   final List<String> tags;
//   final String description;
//   final String role;
//   final String bio;
//   final String leasePeriod;
//   final String frequency;
//   final String level;
//   final List<String> otherPhotos;

//   UserData({
//     required this.id,
//     required this.name,
//     required this.imagePath,
//     required this.isNewHere,
//     required this.tags,
//     required this.description,
//     required this.role,
//     required this.bio,
//     required this.leasePeriod,
//     required this.frequency,
//     required this.level,
//     this.otherPhotos = const [],
//   });
// }
