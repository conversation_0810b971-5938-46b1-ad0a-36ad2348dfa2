import 'package:room_eight/core/utils/app_exports.dart';

class RoomEight {
  static void show({
    required String message,
    ToastificationType type = ToastificationType.success,
    Duration duration = const Duration(seconds: 3),
    bool showProgressBar = false,
  }) {
    toastification.show(
      type: type,
      showProgressBar: showProgressBar,
      style: ToastificationStyle.flatColored,
      title: Text(
        message,
        style: GoogleFonts.montserrat(
          fontSize: 15.0.sp,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 5,
      ),
      autoCloseDuration: duration,
    );
  }
}
