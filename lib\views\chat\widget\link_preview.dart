
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:room_eight/core/utils/app_exports.dart';

class LinkPreview extends StatefulWidget {
  const LinkPreview({
    super.key,
    required this.url,
  });

  final String url;

  @override
  State<LinkPreview> createState() => _LinkPreviewState();
}

class _LinkPreviewState extends State<LinkPreview> {
  bool _isUrlValid = false;
  bool _isCheckingUrl = true;

  @override
  void initState() {
    super.initState();
    _checkUrlExistence();
  }

  Future<void> _checkUrlExistence() async {
    try {
      final response = await http.head(Uri.parse(widget.url));
      if (response.statusCode == 200) {
        setState(() {
          _isUrlValid = true;
        });
      }
    } catch (e) {
      _isUrlValid = false;
    } finally {
      setState(() {
        _isCheckingUrl = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingUrl) {
      return const Center(
        child: CupertinoActivityIndicator(
          color: Colors.white,
        ),
      );
    }

    if (!_isUrlValid) {
      return Text(
        widget.url,
        style: GoogleFonts.montserrat(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.0.h),
            child: 
            // widget.url.isEmpty
            //     ?
                 InkWell(
                    onTap: _onLinkTap,
                    child: Image.network(
                      widget.url,
                      height: 120.h,
                      width: double.infinity,
                      fit: BoxFit.fitWidth,
                    ),
                  )
          //       : AnyLinkPreview(
          //           link: widget.url,
          //           displayDirection: UIDirection.uiDirectionHorizontal,
          //           removeElevation: true,
          //           onTap: _onLinkTap,
          //           backgroundColor:
          //               Theme.of(context).primaryColor.withValues(alpha: 0.6),
          //           bodyStyle: Theme.of(context).textTheme.bodyMedium,
          //           titleStyle: Theme.of(context).textTheme.bodyMedium),
          ),
          
          SizedBox(height: 4.0.h),
          InkWell(
            onTap: _onLinkTap,
            child: Text(widget.url,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Color(0XFF0072b1),
                      decoration: TextDecoration.underline,
                      decorationColor: Color(0XFF0072b1),
                    )),
          ),
        ],
      ),
    );
  }

  void _onLinkTap() {
    if (_isUrlValid) {
      _launchURL();
    }
  }

  void _launchURL() async {
    final parsedUrl = Uri.parse(widget.url);
    // await launchUrl(parsedUrl);
  }
}
