import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:room_eight/core/generated/assets.gen.dart';
import 'package:room_eight/core/themes/custom_color_extension.dart';
import 'package:room_eight/core/utils/navigator_service.dart';
import 'package:room_eight/widgets/common_widget/image_view.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class RoomEightAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final Color? shadowColor;
  final Color? iconColor;
  final double elevation;
  final bool? centerTitle;
  final double? toolbarHeight;
  final bool useGradientLeading;

  const RoomEightAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.shadowColor,
    this.iconColor,
    this.elevation = 0.0,
    this.centerTitle = false,
    this.toolbarHeight,
    this.useGradientLeading = false,
  });

  @override
  Widget build(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return AppBar(
      surfaceTintColor: Colors.transparent,
      backgroundColor: backgroundColor ?? customColors.fillColor,
      automaticallyImplyLeading: false,
      centerTitle: centerTitle,
      leading: showBackButton
          ? useGradientLeading
                ? Center(
                    child: Container(
                      margin: const EdgeInsets.only(left: 16.0),
                      height: 36.h,
                      width: 36.w,
                      child: CustomGradientContainer(
                        height: 36.h,
                        width: 36.w,
                        topColor: customColors.fillColor!,
                        bottomColor: customColors.fillColor!,
                        fillColor: customColors.fillColor!,
                        child: CustomImageView(
                          imagePath: Assets.images.svgs.icons.icBackArrow.path,
                          onTap:
                              onBackPressed ?? () => NavigatorService.goBack(),
                        ),
                      ),
                    ),
                  )
                : CustomImageView(
                    imagePath: Assets.images.svgs.icons.icBackArrow.path,
                    margin: const EdgeInsets.all(15),
                    onTap: onBackPressed ?? () => NavigatorService.goBack(),
                  )
          : null,
      elevation: elevation,
      shadowColor:
          shadowColor?.withValues(alpha: 0.18) ??
          customColors.greycontainercolor?.withValues(alpha: 0.12),
      titleSpacing: 16.0,
      title:
          titleWidget ??
          (title != null
              ? Text(
                  title!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 17.0.sp,
                    fontWeight: FontWeight.bold,
                    color: customColors.blackColor,
                  ),
                )
              : null),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);
}
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:room_eight/core/generated/assets.gen.dart';
// import 'package:room_eight/core/themes/custom_color_extension.dart';
// import 'package:room_eight/core/utils/navigator_service.dart';
// import 'package:room_eight/widgets/common_widget/image_view.dart';

// class RoomEightAppBar extends StatelessWidget implements PreferredSizeWidget {
//   final String? title;
//   final Widget? titleWidget;
//   final bool showBackButton;
//   final VoidCallback? onBackPressed;
//   final List<Widget>? actions;
//   final Color? backgroundColor;
//   final Color? shadowColor;
//   final Color? iconColor;
//   final double elevation;
//   final bool? centerTitle;
//   final double? toolbarHeight;

//   const RoomEightAppBar({
//     super.key,
//     this.title,
//     this.titleWidget,
//     this.showBackButton = true,
//     this.onBackPressed,
//     this.actions,
//     this.backgroundColor,
//     this.shadowColor,
//     this.iconColor,
//     this.elevation = 6.0,
//     this.centerTitle = false,
//     this.toolbarHeight,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return AppBar(
//       surfaceTintColor: Colors.transparent,
//       backgroundColor:
//           backgroundColor ?? Theme.of(context).customColors.fillColor,
//       automaticallyImplyLeading: false,
//       centerTitle: centerTitle,
//       leading: showBackButton
//           ? CustomImageView(
//               imagePath: Assets.images.svgs.icons.icBackArrow.path,
//               margin: EdgeInsets.all(15),
//               onTap: onBackPressed ?? () => NavigatorService.goBack(),
//             )
//           : null,
//       elevation: elevation,
//       shadowColor:
//           shadowColor?.withValues(alpha: 0.18) ??
//           Theme.of(
//             context,
//           ).customColors.greycontainercolor?.withValues(alpha: 0.12),
//       // shape: const RoundedRectangleBorder(
//       //   borderRadius: BorderRadius.vertical(bottom: Radius.circular(24.0)),
//       // ),
//       titleSpacing: 0.0,
//       title:
//           titleWidget ??
//           (title != null
//               ? Text(
//                   title!,
//                   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                     fontSize: 17.0.sp,
//                     fontWeight: FontWeight.bold,
//                     color: Theme.of(context).customColors.blackColor,
//                   ),
//                 )
//               : null),
//       actions: actions,
//     );
//   }

//   @override
//   Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);
// }
