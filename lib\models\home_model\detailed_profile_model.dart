class DetailedProfileModel {
  bool? status;
  String? message;
  ProfileData? data;

  DetailedProfileModel({this.status, this.message, this.data});

  factory DetailedProfileModel.fromJson(Map<String, dynamic> json) {
    return DetailedProfileModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? ProfileData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message, 'data': data?.toJson()};
  }
}

class ProfileData {
  int? id;
  String? profileImage;
  String? name;
  String? gender;
  String? dob;
  String? cleaniness;
  String? leasePeriod;
  String? preferedGender;
  String? preferedLocations;
  String? personalityTypeDescription;
  String? habitsLifestyle;
  String? livingStyle;
  String? interestsHobbies;
  String? contactNumber;
  bool? isVerified;
  bool? isActive;
  String? year;
  String? about;
  String? smoker;
  bool? pet;
  List<String>? additionalProfilesImages;

  ProfileData({
    this.id,
    this.profileImage,
    this.name,
    this.gender,
    this.dob,
    this.cleaniness,
    this.leasePeriod,
    this.preferedGender,
    this.preferedLocations,
    this.personalityTypeDescription,
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
    this.contactNumber,
    this.isVerified,
    this.isActive,
    this.year,
    this.about,
    this.smoker,
    this.pet,
    this.additionalProfilesImages,
  });

  factory ProfileData.fromJson(Map<String, dynamic> json) {
    return ProfileData(
      id: json['id'],
      profileImage: json['profile_image'],
      name: json['name'],
      gender: json['gender'],
      dob: json['dob'],
      cleaniness: json['cleaniness'],
      leasePeriod: json['lease_period'],
      preferedGender: json['prefered_gender'],
      preferedLocations: json['prefered_locations'],
      personalityTypeDescription: json['personality_type_description'],
      habitsLifestyle: json['habits_lifestyle'],
      livingStyle: json['living_style'],
      interestsHobbies: json['interests_hobbies'],
      contactNumber: json['contact_number'],
      isVerified: json['is_verified'],
      isActive: json['is_active'],
      year: json['year'],
      about: json['about'],
      smoker: json['smoker'],
      pet: json['pet'],
      additionalProfilesImages: json['additional_profiles_images'] != null
          ? List<String>.from(json['additional_profiles_images'])
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profile_image': profileImage,
      'name': name,
      'gender': gender,
      'dob': dob,
      'cleaniness': cleaniness,
      'lease_period': leasePeriod,
      'prefered_gender': preferedGender,
      'prefered_locations': preferedLocations,
      'personality_type_description': personalityTypeDescription,
      'habits_lifestyle': habitsLifestyle,
      'living_style': livingStyle,
      'interests_hobbies': interestsHobbies,
      'contact_number': contactNumber,
      'is_verified': isVerified,
      'is_active': isActive,
      'year': year,
      'about': about,
      'smoker': smoker,
      'pet': pet,
      'additional_profiles_images': additionalProfilesImages,
    };
  }
}

// class DetailedProfileModel {
//   bool? status;
//   String? message;
//   ProfileData? data;

//   DetailedProfileModel({this.status, this.message, this.data});

//   factory DetailedProfileModel.fromJson(Map<String, dynamic> json) {
//     return DetailedProfileModel(
//       status: json['status'],
//       message: json['message'],
//       data: json['data'] != null ? ProfileData.fromJson(json['data']) : null,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {'status': status, 'message': message, 'data': data?.toJson()};
//   }
// }

// class ProfileData {
//   int? id;
//   String? profileImage;
//   String? year;
//   String? about;
//   String? smoker;
//   bool? pet;
//   List<String>? additionalProfilesImages;

//   ProfileData({
//     this.id,
//     this.profileImage,
//     this.year,
//     this.about,
//     this.smoker,
//     this.pet,
//     this.additionalProfilesImages,
//   });

//   factory ProfileData.fromJson(Map<String, dynamic> json) {
//     return ProfileData(
//       id: json['id'],
//       profileImage: json['profile_image'],
//       year: json['year'],
//       about: json['about'],
//       smoker: json['smoker'],
//       pet: json['pet'],
//       additionalProfilesImages: json['additional_profiles_images'] != null
//           ? List<String>.from(json['additional_profiles_images'])
//           : [],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'profile_image': profileImage,
//       'year': year,
//       'about': about,
//       'smoker': smoker,
//       'pet': pet,
//       'additional_profiles_images': additionalProfilesImages,
//     };
//   }
// }
