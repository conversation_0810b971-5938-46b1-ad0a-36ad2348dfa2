part of 'onboarding_bloc.dart';

abstract class OnboardingState extends Equatable {
  const OnboardingState();

  @override
  List<Object> get props => [];
}

class OnboardingInitial extends OnboardingState {
  final int currentPage;

  const OnboardingInitial({this.currentPage = 0});

  @override
  List<Object> get props => [currentPage];
}

class OnboardingPageUpdated extends OnboardingState {
  final int currentPage;

  const OnboardingPageUpdated(this.currentPage);

  @override
  List<Object> get props => [currentPage];
}

class OnboardingNavigateToLogin extends OnboardingState {}
