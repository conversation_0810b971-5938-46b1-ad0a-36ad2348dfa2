class ConfirmDeleteAccountModel {
  final bool status;
  final String message;

  ConfirmDeleteAccountModel({required this.status, required this.message});

  factory ConfirmDeleteAccountModel.fromJson(Map<String, dynamic> json) {
    return ConfirmDeleteAccountModel(
      status: json['status'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}
