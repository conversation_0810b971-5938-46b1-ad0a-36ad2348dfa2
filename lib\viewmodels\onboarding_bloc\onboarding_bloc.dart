import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';

part 'onboarding_event.dart';
part 'onboarding_state.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  OnboardingBloc() : super(const OnboardingInitial()) {
    on<OnboardingPageChanged>(_onPageChanged);
    on<OnboardingNextPage>(_onNextPage);
    on<OnboardingSkip>(_onSkip);
    on<OnboardingContinue>(_onContinue);
  }

  void _onPageChanged(
    OnboardingPageChanged event,
    Emitter<OnboardingState> emit,
  ) {
    emit(OnboardingPageUpdated(event.pageIndex));
  }

  void _onNextPage(OnboardingNextPage event, Emitter<OnboardingState> emit) {
    final currentPage = _getCurrentPage();
    if (currentPage < 1) {
      emit(OnboardingPageUpdated(currentPage + 1));
    }
  }

  void _onSkip(OnboardingSkip event, Emitter<OnboardingState> emit) {
    Prefobj.preferences?.put(Prefkeys.ONBOARDING, true);
    emit(OnboardingNavigateToLogin());
  }

  void _onContinue(OnboardingContinue event, Emitter<OnboardingState> emit) {
    final currentPage = _getCurrentPage();
    if (currentPage == 1) {
      Prefobj.preferences?.put(Prefkeys.ONBOARDING, true);
      emit(OnboardingNavigateToLogin());
    } else {
      emit(OnboardingPageUpdated(currentPage + 1));
    }
  }

  int _getCurrentPage() {
    if (state is OnboardingPageUpdated) {
      return (state as OnboardingPageUpdated).currentPage;
    }
    if (state is OnboardingInitial) {
      return (state as OnboardingInitial).currentPage;
    }
    return 0;
  }
}
