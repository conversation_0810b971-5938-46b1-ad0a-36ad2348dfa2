part of 'chat_bloc.dart';

sealed class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object> get props => [];
}

class ChatInitialEvent extends ChatEvent {
  @override
  List<Object> get props => [];
}

class SendMessageEvent extends ChatEvent {
  final int touserId;
  final String message;
  final String type;
  final String file;
  const SendMessageEvent({
    required this.touserId,
    required this.message,
    required this.type,
    required this.file,
  });

  @override
  List<Object> get props => [touserId, message, type, file];
}

class ReceiveMessageEvent extends ChatEvent {
  const ReceiveMessageEvent();
  @override
  List<Object> get props => [];
}

class GetChatListEvent extends ChatEvent {
  final int page;
  const GetChatListEvent({required this.page});
  @override
  List<Object> get props => [page];
}

class GetChatMessageListEvent extends ChatEvent {
  final int page;
  final String userId;
  const GetChatMessageListEvent({required this.page, required this.userId});
  @override
  List<Object> get props => [page, userId];
}

class UpdateChatMessageSocketEvent extends ChatEvent {
  final int id;
  final String message;
  final String type;
  final String createdat;
  final int sentby;
  const UpdateChatMessageSocketEvent({
    required this.id,
    required this.message,
    required this.type,
    required this.createdat,
    required this.sentby,
  });
  @override
  List<Object> get props => [id, message, type, createdat, sentby];
}

class UpdateChatMessageListDirectEvent extends ChatEvent {
  final List<ChatMessageData> messageList;

  const UpdateChatMessageListDirectEvent({required this.messageList});

  @override
  List<Object> get props => [messageList];
}

class TypingSocketEvent extends ChatEvent {
  final int userId;
  final String isTyping;

  const TypingSocketEvent({required this.userId, required this.isTyping});
  @override
  List<Object> get props => [userId, isTyping];
}

class SearchUserListEvent extends ChatEvent {
  final String searchtext;
  const SearchUserListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}

class ClearChatTextFieldEvent extends ChatEvent {
  const ClearChatTextFieldEvent();
  @override
  List<Object> get props => [];
}

class ReadMessageEvent extends ChatEvent {
  final int touserId;
  final int messageId;
  const ReadMessageEvent({required this.touserId, required this.messageId});

  @override
  List<Object> get props => [touserId, messageId];
}

class ChatListMessageEvent extends ChatEvent {
  final int page;
  const ChatListMessageEvent({required this.page});

  @override
  List<Object> get props => [page];
}

class DeleteChatApiEvent extends ChatEvent {
  final int userId;

  const DeleteChatApiEvent({required this.userId});
  @override
  List<Object> get props => [userId];
}

class RefreshChatGetApiEvent extends ChatEvent {
  final String userId;

  const RefreshChatGetApiEvent({required this.userId});

  @override
  List<Object> get props => [userId];
}
