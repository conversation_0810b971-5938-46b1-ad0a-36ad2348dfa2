class SelectionOptionModel {
  bool? status;
  String? message;
  SelectionData? data;

  SelectionOptionModel({this.status, this.message, this.data});

  factory SelectionOptionModel.fromJson(Map<String, dynamic> json) {
    return SelectionOptionModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? SelectionData.fromJson(json['data']) : null,
    );
  }
}

class SelectionData {
  List<ProfileOptionModel>? habitsLifestyle;
  List<ProfileOptionModel>? livingStyle;
  List<ProfileOptionModel>? interestsHobbies;

  SelectionData({
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
  });

  factory SelectionData.fromJson(Map<String, dynamic> json) {
    return SelectionData(
      habitsLifestyle: (json['habits_lifestyle'] as List?)
          ?.map((e) => ProfileOptionModel.fromJson(e))
          .toList(),
      livingStyle: (json['living_style'] as List?)
          ?.map((e) => ProfileOptionModel.from<PERSON><PERSON>(e))
          .toList(),
      interestsHobbies: (json['interests_hobbies'] as List?)
          ?.map((e) => ProfileOptionModel.fromJson(e))
          .toList(),
    );
  }
}

class ProfileOptionModel {
  int? id;
  String? name;
  String? icon;

  ProfileOptionModel({this.id, this.name, this.icon});

  factory ProfileOptionModel.fromJson(Map<String, dynamic> json) {
    return ProfileOptionModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}
