import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';

class CustomGradientContainer extends StatelessWidget {
  final double height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final Color topColor;
  final Color bottomColor;
  final Color fillColor;
  final Widget? child;
  final VoidCallback? onTap;
  final bool enableBlur;
  final double blurSigma;

  const CustomGradientContainer({
    super.key,
    required this.height,
    this.width,
    this.padding,
    required this.topColor,
    required this.bottomColor,
    required this.fillColor,
    this.child,
    this.onTap,
    this.enableBlur = false,
    this.blurSigma = 10.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.translucent,
      child: IntrinsicWidth(
        child: CustomPaint(
          painter: _GradientBorderPainter(
            topColor: topColor,
            bottomColor: bottomColor,
            strokeWidth: 1.0,
            borderRadius: BorderRadius.circular(100.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(100.r),
            child: enableBlur
                ? BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: blurSigma,
                      sigmaY: blurSigma,
                    ),
                    child: _innerContainer(Colors.transparent),
                  )
                : _innerContainer(fillColor),
          ),
        ),
      ),
    );
  }

  Widget _innerContainer(Color backgroundColor) {
    return Container(
      height: height,
      width:
          width, // This will be null if not provided, allowing intrinsic sizing
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 6.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Center(child: child ?? const SizedBox()),
    );
  }
}

class _GradientBorderPainter extends CustomPainter {
  final Color topColor;
  final Color bottomColor;
  final double strokeWidth;
  final BorderRadius borderRadius;

  _GradientBorderPainter({
    required this.topColor,
    required this.bottomColor,
    required this.strokeWidth,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;
    final paint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [topColor, bottomColor],
      ).createShader(rect);

    final rrect = borderRadius.toRRect(rect).deflate(strokeWidth / 2);
    canvas.drawRRect(rrect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}