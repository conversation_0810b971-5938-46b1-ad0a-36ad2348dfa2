import 'package:room_eight/core/utils/app_exports.dart';

/// Utility class for managing cache operations and handling corrupted cache files
class CacheUtils {
  /// Clear all cache managers to resolve corrupted cache issues
  static Future<void> clearAllCaches() async {
    final List<String> cacheKeys = [
      'RoomEight',
      'RoomEight_V2',
      'RoomEight_V3',
    ];

    for (String cacheKey in cacheKeys) {
      try {
        final cacheManager = CacheManager(
          Config(
            cacheKey,
            repo: JsonCacheInfoRepository(databaseName: cacheKey),
          ),
        );
        await cacheManager.emptyCache();
        Logger.lOG('Successfully cleared cache: $cacheKey');
      } catch (e) {
        Logger.lOG('Error clearing cache $cacheKey: $e');
      }
    }
  }

  /// Clear specific cache by key
  static Future<void> clearCacheByKey(String cacheKey) async {
    try {
      final cacheManager = CacheManager(
        Config(cacheKey, repo: JsonCacheInfoRepository(databaseName: cacheKey)),
      );
      await cacheManager.emptyCache();
      Logger.lOG('Successfully cleared cache: $cacheKey');
    } catch (e) {
      Logger.lOG('Error clearing cache $cacheKey: $e');
    }
  }

  /// Get cache info for debugging
  static Future<void> getCacheInfo(String cacheKey) async {
    try {
      final cacheManager = CacheManager(
        Config(cacheKey, repo: JsonCacheInfoRepository(databaseName: cacheKey)),
      );

      final cacheObjects = await cacheManager.getFileFromCache(cacheKey);
      Logger.lOG('Cache info for $cacheKey: ${cacheObjects?.toString()}');
    } catch (e) {
      Logger.lOG('Error getting cache info for $cacheKey: $e');
    }
  }
}
