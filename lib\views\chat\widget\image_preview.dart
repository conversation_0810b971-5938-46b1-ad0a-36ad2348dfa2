
import 'package:room_eight/core/utils/app_exports.dart';

class FlowkarImagePreview extends StatefulWidget {
  final String imagepath;
  const FlowkarImagePreview({super.key, required this.imagepath});

  @override
  State<FlowkarImagePreview> createState() => _FlowkarImagePreviewState();
}

class _FlowkarImagePreviewState extends State<FlowkarImagePreview> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // FlowkarAppbar(
          //   title: Lang.of(context).lbl_preview,
          //   hasLeadingIcon: true,
          // ),
          const Spacer(),
          CustomImageView(
            imagePath: widget.imagepath,
            height: MediaQuery.of(context).size.height / 2,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
          const Spacer(flex: 1),
        ],
      ),
    );
  }
}
