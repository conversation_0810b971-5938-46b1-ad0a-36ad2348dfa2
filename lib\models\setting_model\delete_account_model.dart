class DeleteAccountModel {
  final bool status;
  final String message;

  DeleteAccountModel({required this.status, required this.message});

  factory DeleteAccountModel.fromJson(Map<String, dynamic> json) {
    return DeleteAccountModel(
      status: json['status'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}
