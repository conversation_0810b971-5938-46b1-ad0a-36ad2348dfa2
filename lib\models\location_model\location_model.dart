class LocationModel {
  final String name;
  final double latitude;
  final double longitude;
  final String? address;

  const LocationModel({
    required this.name,
    required this.latitude,
    required this.longitude,
    this.address,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      name: json['name'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      address: json['address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
    };
  }

  // Convert to API format
  Map<String, dynamic> toApiFormat() {
    return {
      'lat': latitude,
      'long': longitude,
    };
  }

  @override
  String toString() {
    return 'LocationModel(name: $name, latitude: $latitude, longitude: $longitude, address: $address)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel &&
        other.name == name &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.address == address;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        address.hashCode;
  }
}
