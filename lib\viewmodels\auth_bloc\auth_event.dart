part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class EmailChanged extends AuthEvent {
  final String email;
  const EmailChanged(this.email);
}

class PasswordChanged extends AuthEvent {
  final String password;
  const PasswordChanged(this.password);
}

class TogglePasswordVisibility extends AuthEvent {}

class ToggleSignupPasswordVisibility extends AuthEvent {}

class ToggleSignupConfirmPasswordVisibility extends AuthEvent {}

class LoginSubmitted extends AuthEvent {}

class SignupSubmitted extends AuthEvent {}

class ToggleAgreementAccepted extends AuthEvent {}

class SignupNameChanged extends AuthEvent {
  final String name;
  const SignupNameChanged(this.name);
}

class SignupEmailChanged extends AuthEvent {
  final String email;
  const SignupEmailChanged(this.email);
}

class SignupPasswordChanged extends AuthEvent {
  final String password;
  const SignupPasswordChanged(this.password);
}

class SignupConfirmPasswordChanged extends AuthEvent {
  final String confirmPassword;
  const SignupConfirmPasswordChanged(this.confirmPassword);
}
