import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';
import 'package:room_eight/widgets/common_widget/exception_widget.dart';
import 'package:room_eight/widgets/custom_widget/custom_transition.dart';

class SearchUserListWidget extends StatefulWidget {
  final List<SearchUserData> userList;
  final TextEditingController searchcontroller;
  final void Function() onclose;
  const SearchUserListWidget({
    super.key,
    this.userList = const [],
    required this.searchcontroller,
    required this.onclose,
  });

  @override
  State<SearchUserListWidget> createState() => _SearchUserListWidgetState();
}

class _SearchUserListWidgetState extends State<SearchUserListWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Column(
          children: [
            Expanded(
              child: widget.userList.isEmpty
                  ? _buildNoChatListFound(themestate)
                  : _buildUserList(themestate, widget.userList),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUserList(ThemeState themestate, List<SearchUserData> userList) {
    return ShowUpTransition(
      forward: true,
      delay: const Duration(milliseconds: 300),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        itemCount: userList.length,
        itemBuilder: (context, index) {
          return _buildUserDetail(context, themestate, index, userList);
        },
      ),
    );
  }

  Widget _buildUserDetail(
    BuildContext context,
    ThemeState themestate,
    int index,
    List<SearchUserData> userList,
  ) {
    return InkWell(
      onTap: () {
        FocusScope.of(context).unfocus();
        widget.searchcontroller.clear();
        NavigatorService.pushNamed(
          AppRoutes.chatscreen,
          arguments: [
            userList[index],
            () {
              widget.onclose();
            },
          ],
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.w),
        child: Container(
          decoration: BoxDecoration(
            color: ThemeData().customColors.primaryColor,
            borderRadius: BorderRadius.circular(40.0.r),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.0.w, vertical: 8.0.h),
            child: Row(
              children: [
                ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: CustomImageView(
                      radius: BorderRadius.circular(30.r),
                      height: 60.0.h,
                      width: 60.0.w,
                      fit: BoxFit.cover,
                      imagePath:
                          userList[index].profileImage == null ||
                              userList[index].profileImage == ""
                          ? Assets.images.pngs.other.pngAppLogo.path
                          : "${SocketConfig.mainbaseURL}${userList[index].profileImage}",
                      alignment: Alignment.center,
                    ),
                  ),
                ),
                buildSizedboxW(8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userList[index].name ?? '',
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      buildSizedBoxH(6.0),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Flexible(
                            child: Text(
                              userList[index].userName ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.headlineSmall!
                                  .copyWith(fontSize: 12.sp),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoChatListFound(ThemeState themestate) {
    return Center(
      child: ExceptionWidget(
        imagePath: Assets.images.pngs.other.pngAppLogo.path,
        title: 'Lang.of(context).lbl_no_result_found',
        subtitle: 'Lang.of(context).lbl_no_result_found_subtitle',
        showButton: false,
      ),
    );
  }
}
