import 'package:room_eight/core/utils/app_exports.dart';

class BackgroundImage extends StatelessWidget {
  final Widget child;
  final String imagePath;
  final bool showGradient;

  const BackgroundImage({
    super.key,
    required this.child,
    required this.imagePath,
    this.showGradient = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomImageView(
          imagePath: imagePath,
          fit: BoxFit.cover,
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
        ),
        if (showGradient)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height:
                  MediaQuery.of(context).size.height *
                  0.33, // 33% of screen height
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.5),
                    Colors.black.withValues(alpha: 0.6),
                    Colors.black.withValues(alpha: 0.7),
                    Colors.black.withValues(alpha: 0.8),
                    Colors.black.withValues(alpha: 0.9),
                  ],
                  stops: const [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
                ),
              ),
            ),
          ),
        child,
      ],
    );
  }
}
