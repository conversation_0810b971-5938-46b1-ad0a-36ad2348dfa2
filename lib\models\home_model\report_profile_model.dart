class ReportProfileModel {
  final bool status;
  final String message;

  ReportProfileModel({required this.status, required this.message});

  factory ReportProfileModel.fromJson(Map<String, dynamic> json) {
    return ReportProfileModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}
