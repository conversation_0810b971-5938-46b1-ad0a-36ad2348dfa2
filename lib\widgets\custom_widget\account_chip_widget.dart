// widgets/custom_widget/account_chip_widget.dart
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/custom_widget/status_chip_widget.dart';

class AccountChipWidget extends StatelessWidget {
  final VoidCallback onTap;
  final String balance;
  final String currency;
  final String statusLabel;
  final bool isVerified;

  const AccountChipWidget({
    super.key,
    required this.onTap,
    this.balance = '10,000.00',
    this.currency = 'USD',
    this.statusLabel = 'Demo',
    this.isVerified = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.50,
        decoration: _buildChipDecoration(context),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              StatusChipWidget(
                statusLabel: statusLabel,
                isVerified: isVerified,
              ),
              buildSizedboxW(8.w),
              Expanded(
                child: Text(
                  '$balance $currency',
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(
                    context,
                  ).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              buildSizedboxW(8.w),
              Icon(Icons.more_vert_outlined, size: 16.sp),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildChipDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).customColors.fillColor,
      borderRadius: BorderRadius.circular(100.r),
      border: Border.all(color: Theme.of(context).customColors.greyborder!),
    );
  }
}
