import 'package:room_eight/core/utils/app_exports.dart';

class ButtonThemeHelper {
  static ButtonStyle greenButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      disabledBackgroundColor: const Color(0xff239C71).withValues(alpha: 0.5),
      backgroundColor: const Color(0xff239C71),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      padding: EdgeInsets.zero,
    );
  }

  static ButtonStyle secondaryButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(100.0),
        side: BorderSide(color: theme.customColors.primaryColor!, width: 1.5),
      ),
      padding: EdgeInsets.zero,
    );
  }
}
