part of 'splash_bloc.dart';

class SplashState extends Equatable {
  final SalesAppAuthIntilizeModel? salesAppAuthIntilizeModel;

  const SplashState({this.salesAppAuthIntilizeModel});

  @override
  List<Object?> get props => [salesAppAuthIntilizeModel];

  SplashState copyWith({SalesAppAuthIntilizeModel? salesAppAuthIntilizeModel}) {
    return SplashState(salesAppAuthIntilizeModel: salesAppAuthIntilizeModel ?? this.salesAppAuthIntilizeModel);
  }
}
