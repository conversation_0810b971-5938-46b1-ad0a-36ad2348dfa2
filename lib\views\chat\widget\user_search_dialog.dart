import 'package:flutter/material.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';
import 'package:room_eight/views/chat/chat_screen.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';
import 'package:room_eight/core/api_config/endpoints/socket_key.dart';

/// Dialog widget that allows users to search for other users and start a chat
class UserSearchDialog extends StatefulWidget {
  const UserSearchDialog({super.key});

  @override
  State<UserSearchDialog> createState() => _UserSearchDialogState();
}

class _UserSearchDialogState extends State<UserSearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  final ValueNotifier<String> _searchText = ValueNotifier('');

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      _searchText.value = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchText.dispose();
    super.dispose();
  }

  void _performSearch(String searchText) {
    if (searchText.trim().isNotEmpty) {
      context.read<ChatBloc>().add(SearchUserListEvent(searchtext: searchText.trim()));
    }
  }

  void _startChatWithUser(SearchUserData user) {
    // Close the dialog first
    Navigator.of(context).pop();
    
    // Start chat with the selected user
    // ChatScreen.startChatWithUser(context, user);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'Search Users',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Search field
            CustomTextInputField(
              controller: _searchController,
              hintLabel: 'Search by username...',
              context: context,
              type: InputType.text,
              prefixIcon: const Icon(Icons.search),
              onChanged: (value) {
                // Debounce search to avoid too many API calls
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (_searchController.text == value && value.trim().isNotEmpty) {
                    _performSearch(value);
                  }
                });
              },
            ),
            const SizedBox(height: 16),
            
            // Search results
            Expanded(
              child: BlocBuilder<ChatBloc, ChatState>(
                builder: (context, state) {
                  if (state.searchuserListLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  
                  if (_searchText.value.isEmpty) {
                    return const Center(
                      child: Text(
                        'Enter a username to search for users',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    );
                  }
                  
                  if (state.searchuserList.isEmpty) {
                    return const Center(
                      child: Text(
                        'No users found',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    itemCount: state.searchuserList.length,
                    itemBuilder: (context, index) {
                      final user = state.searchuserList[index];
                      return _buildUserTile(user);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserTile(SearchUserData user) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: user.profileImage != null && user.profileImage!.isNotEmpty
            ? NetworkImage('${SocketConfig.mainbaseURL}${user.profileImage}')
            : null,
        child: user.profileImage == null || user.profileImage!.isEmpty
            ? Text(
                user.name?.isNotEmpty == true 
                    ? user.name![0].toUpperCase() 
                    : user.userName?.isNotEmpty == true 
                        ? user.userName![0].toUpperCase()
                        : 'U',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              )
            : null,
        backgroundColor: Theme.of(context).primaryColor,
      ),
      title: Text(
        user.name ?? user.userName ?? 'Unknown User',
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: user.userName != null
          ? Text('@${user.userName}')
          : null,
      trailing: ElevatedButton(
        onPressed: () => _startChatWithUser(user),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('Chat'),
      ),
      onTap: () => _startChatWithUser(user),
    );
  }
}

/// Extension to easily show the user search dialog
extension UserSearchDialogExtension on BuildContext {
  void showUserSearchDialog() {
    showDialog(
      context: this,
      builder: (context) => const UserSearchDialog(),
    );
  }
}
