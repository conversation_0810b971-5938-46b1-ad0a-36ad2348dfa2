// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/like_model/get_like_profile_Inward_model.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  final LikeRepository likeRepository;
  LikeBloc(this.likeRepository) : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
    on<GetLikeProfilInward>(_onLoadLikeAndDisLikeData);
    on<GetLikeProfileOutwards>(_onLoadDisLikeData);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  void _onLoadLikeAndDisLikeData(
    GetLikeProfilInward event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadData: true));
    GetLikeProfileInwardModel like = await likeRepository.getMoveInData();

    if (like.status == true) {
      final List<LikeAndDisLikeUsers> likedata = like.data ?? [];

      emit(state.copyWith(moveInData: likedata, isLoadData: false));
    }
  }

  void _onLoadDisLikeData(
    GetLikeProfileOutwards event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadData: true));
    GetLikeProfileInwardModel disLike = await likeRepository.getMoveOutData();

    if (disLike.status == true) {
      final List<LikeAndDisLikeUsers> disLikedata = disLike.data ?? [];

      emit(state.copyWith(moveOutData: disLikedata, isLoadData: false));


      // void _onLoadMoveInData(
      //   LoadMoveInData event,
      //   Emitter<LikeState> emit,
      // ) async {
      //   emit(state.copyWith(isLoadingMoveIn: true));
      //   try {
      //     MoveInModel moveIn = await likeRepository.getMoveInData();

      //     if (moveIn.status == true) {
      //       final List<MoveInUser> moveInData = moveIn.data ?? [];

      //       emit(
      //         state.copyWith(moveInData: moveInData, isLoadingMoveIn: false),
      //       );
      //     } else {
      //       emit(state.copyWith(isLoadingMoveIn: false));
      //     }
      //   } catch (e) {
      //     emit(state.copyWith(isLoadingMoveIn: false));
      //   }
      // }
      void _onLoadMoveOutData(
        LoadMoveOutData event,
        Emitter<LikeState> emit,
      ) async {
        emit(state.copyWith(isLoadingMoveOut: true));
        try {
          GetLikeProfileInwardModel moveOut = await likeRepository.getMoveOutData();

          if (moveOut.status == true) {
            final List<LikeAndDisLikeUsers> moveOutData = moveOut.data ?? [];

            emit(
              state.copyWith(moveOutData: moveOutData, isLoadingMoveOut: false),
            );
          } else {
            emit(state.copyWith(isLoadingMoveOut: false));
          }
        } catch (e) {
          emit(state.copyWith(isLoadingMoveOut: false));
        }
      }
      void _onAcceptLikeEvent(
        AcceptLikeEvent event,
        Emitter<LikeState> emit,
      ) async {
        emit(state.copyWith(isAcceptingLike: true));
        try {
          final result = await likeRepository.acceptLike(
            likeProfileId: event.profileId,
            isAccept: event.isAccept,
          );

          if (result.status == true) {
            // Remove the user from moveInData after successful accept/reject
            final updatedMoveInData = state.moveInData
                .where((user) => user.id != event.profileId)
                .toList();

            emit(
              state.copyWith(
                moveInData: updatedMoveInData,
                isAcceptingLike: false,
                shouldNavigateToChat: event.isAccept && event.user != null,
                acceptedUser: event.isAccept ? event.user : null,
                toMessageId: event.isAccept ? (event.user?.toMessageId) : null,
              ),
            );

            // Convert MoveInUser to SearchUserData for chat screen compatibility
            if (event.isAccept && event.user != null) {
              final chatUser = SearchUserData(
                userId:
                    event.user!.toMessageId ??
                    event.user!.id, // Use toMessageId or fallback to id
                name: event.user!.name,
                userName: event
                    .user!
                    .name, // Using name as username since MoveInUser doesn't have username
                profileImage: event.user!.profileImage,
              );

              NavigatorService.pushNamed(
                AppRoutes.chatscreen,
                arguments: [chatUser, () {}], // Pass as array with callback
              );
            }
          } else {
            emit(state.copyWith(isAcceptingLike: false));
          }
        } catch (e) {
          emit(state.copyWith(isAcceptingLike: false));
        }
      }

      void _onResetNavigationEvent(
        ResetNavigationEvent event,
        Emitter<LikeState> emit,
      ) {
        emit(state.resetNavigation());
      }

      @override
      Future<void> close() {
        pageController.dispose();
        return super.close();
      }
    }
  }
}
