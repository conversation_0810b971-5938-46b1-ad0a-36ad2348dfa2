class MessageListModel {
  final int count;
  final String? next;
  final String? previous;
  final Results results;

  MessageListModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory MessageListModel.fromJson(Map<String, dynamic> json) {
    return MessageListModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: Results.from<PERSON>son(json['results']),
    );
  }
}

class Results {
  final bool status;
  final String message;
  final List<dynamic> data;

  Results({required this.status, required this.message, required this.data});

  factory Results.fromJson(Map<String, dynamic> json) {
    return Results(
      status: json['status'],
      message: json['message'],
      data: json['data'] ?? [],
    );
  }
}
